import { createRouter, createWebHistory } from 'vue-router'

// 路由组件懒加载
const Home = () => import('@/views/Home.vue')
const Login = () => import('@/views/auth/Login.vue')
const Register = () => import('@/views/auth/Register.vue')
const Questions = () => import('@/views/questions/Questions.vue')
const QuestionDetail = () => import('@/views/questions/QuestionDetail.vue')
const AskQuestion = () => import('@/views/questions/AskQuestion.vue')
const Profile = () => import('@/views/user/Profile.vue')
const Membership = () => import('@/views/membership/Membership.vue')
const Resources = () => import('@/views/resources/Resources.vue')
const Admin = () => import('@/views/admin/Admin.vue')
const NotFound = () => import('@/views/error/NotFound.vue')

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '首页',
      keepAlive: true
    }
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('../views/About.vue')
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录',
      hideForAuth: true
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: {
      title: '注册',
      hideForAuth: true
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: {
      title: '个人中心',
      requiresAuth: true
    }
  },
  {
    path: '/questions',
    name: 'Questions',
    component: Questions,
    meta: {
      title: '问答',
      keepAlive: true
    }
  },
  {
    path: '/questions/:id',
    name: 'QuestionDetail',
    component: QuestionDetail,
    meta: {
      title: '问题详情'
    }
  },
  {
    path: '/ask',
    name: 'AskQuestion',
    component: AskQuestion,
    meta: {
      title: '提问',
      requiresAuth: true
    }
  },
  {
    path: '/resources',
    name: 'Resources',
    component: Resources,
    meta: {
      title: '资源库'
    }
  },
  {
    path: '/resources/:id',
    name: 'ResourceDetail',
    component: () => import('../views/ResourceDetail.vue')
  },
  {
    path: '/search',
    name: 'Search',
    component: () => import('../views/Search.vue')
  },
  {
    path: '/data-analysis',
    name: 'DataAnalysis',
    component: () => import('../views/DataAnalysis.vue')
  },
  {
    path: '/databases',
    name: 'Databases',
    component: () => import('../views/Databases.vue')
  },
  {
    path: '/databases/:type',
    name: 'DatabaseDetail',
    component: () => import('../views/DatabaseDetail.vue')
  },
  {
    path: '/structure-viewer',
    name: 'StructureViewer',
    component: () => import('../views/StructureViewer.vue')
  },
  {
    path: '/downloads',
    name: 'Downloads',
    component: () => import('../views/Downloads.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/forum',
    name: 'Forum',
    component: () => import('../views/Forum.vue')
  },
  {
    path: '/forum/topic/:id',
    name: 'ForumTopic',
    component: () => import('../views/ForumTopic.vue')
  },
  {
    path: '/admin',
    name: 'Admin',
    component: Admin,
    meta: {
      title: '管理后台',
      requiresAuth: true,
      requiresAdmin: true
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: '页面未找到'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth'
      }
    } else {
      return { top: 0 }
    }
  }
})

export default router
