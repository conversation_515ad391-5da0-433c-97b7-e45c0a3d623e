import axios from 'axios'
import { ElMessage } from 'element-plus'
import { getToken, removeToken } from '@/utils/auth'
import router from '@/router'

// 导入模拟数据
import mockUsers from './mock/users'
import mockQuestions from './mock/questions'
import mockAnswers from './mock/answers'
import mockResources from './mock/resources'
import mockDatabases from './mock/databases'

// 检查是否使用模拟API
const useMockApi = import.meta.env.VITE_USE_MOCK_API === 'true'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const { data } = response
    
    // 如果是文件下载，直接返回response
    if (response.config.responseType === 'blob') {
      return response
    }

    // 检查业务状态码
    if (data.success === false) {
      // 根据错误码进行不同处理
      switch (data.code) {
        case 401:
          ElMessage.error('登录已过期，请重新登录')
          removeToken()
          router.push('/login')
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 429:
          ElMessage.error('请求过于频繁，请稍后再试')
          break
        default:
          ElMessage.error(data.message || '请求失败')
      }
      
      return Promise.reject(new Error(data.message || '请求失败'))
    }

    return data
  },
  (error) => {
    console.error('响应拦截器错误:', error)
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          ElMessage.error('登录已过期，请重新登录')
          removeToken()
          router.push('/login')
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 429:
          ElMessage.error('请求过于频繁，请稍后再试')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        case 502:
          ElMessage.error('网关错误')
          break
        case 503:
          ElMessage.error('服务暂时不可用')
          break
        default:
          ElMessage.error(data?.message || `请求失败 (${status})`)
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，请检查网络连接')
    } else if (error.message === 'Network Error') {
      ElMessage.error('网络连接失败，请检查网络')
    } else {
      ElMessage.error(error.message || '请求失败')
    }
    
    return Promise.reject(error)
  }
)

// 模拟API响应
const mockResponse = (data, delay = 300) => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({ 
        success: true, 
        data,
        message: 'success' 
      })
    }, delay)
  })
}

// API接口定义
const api = {
  // 数据库相关API
  databases: {
    getAll: () => {
      if (useMockApi) {
        return mockResponse(mockDatabases)
      }
      return request.get('/databases')
    },
    
    getById: (id) => {
      if (useMockApi) {
        const database = mockDatabases.find(db => db.id === id)
        return mockResponse(database || {})
      }
      return request.get(`/databases/${id}`)
    }
  },
  
  // 认证相关
  auth: {
    login: (data) => {
      if (useMockApi) {
        // 模拟登录，任何用户名和密码都能登录
        const user = { 
          id: 1, 
          username: data.username, 
          email: `${data.username}@example.com`,
          role: data.username === 'admin' ? 'admin' : 'user',
          isAdmin: data.username === 'admin',
          isMember: data.username === 'admin',
          accessToken: 'mock-token-123456789',
          refreshToken: 'mock-refresh-token-123456789'
        }
        localStorage.setItem('access_token', user.accessToken)
        return mockResponse(user)
      }
      return request.post('/auth/login', data)
    },
    register: (data) => {
      if (useMockApi) {
        const user = { 
          id: Date.now(), 
          username: data.username, 
          email: data.email,
          role: 'user',
          token: 'mock-token-' + Date.now()
        }
        return mockResponse(user)
      }
      return request.post('/auth/register', data)
    },
    logout: () => {
      if (useMockApi) {
        localStorage.removeItem('token')
        return mockResponse(true)
      }
      return request.post('/auth/logout')
    },
    refresh: (data) => {
      if (useMockApi) {
        return mockResponse({ token: 'mock-token-' + Date.now() })
      }
      return request.post('/auth/refresh', data)
    },
    me: () => {
      if (useMockApi) {
        const token = localStorage.getItem('token')
        if (!token) {
          return Promise.reject({ response: { status: 401 } })
        }
        // 根据localStorage中保存的用户名生成用户信息
        return mockResponse(mockUsers.find(u => u.id === 1) || mockUsers[0])
      }
      return request.get('/auth/me')
    }
  },

  // 用户相关
  users: {
    getProfile: () => {
      if (useMockApi) {
        return mockResponse(mockUsers.find(u => u.id === 1) || mockUsers[0])
      }
      return request.get('/users/profile')
    },
    updateProfile: (data) => {
      if (useMockApi) {
        return mockResponse({ ...mockUsers.find(u => u.id === 1), ...data })
      }
      return request.put('/users/profile', data)
    },
    changePassword: (data) => {
      if (useMockApi) {
        return mockResponse({ success: true })
      }
      return request.put('/users/password', data)
    },
    uploadAvatar: (data) => {
      if (useMockApi) {
        return mockResponse({ url: 'https://via.placeholder.com/150' })
      }
      return request.post('/users/avatar', data)
    }
  },

  // 问题相关
  questions: {
    list: (params) => {
      if (useMockApi) {
        return mockResponse({
          items: mockQuestions,
          total: mockQuestions.length
        })
      }
      return request.get('/questions', { params })
    },
    detail: (id) => {
      if (useMockApi) {
        return mockResponse(mockQuestions.find(q => q.id === Number(id)) || mockQuestions[0])
      }
      return request.get(`/questions/${id}`)
    },
    create: (data) => {
      if (useMockApi) {
        const newQuestion = {
          id: Date.now(),
          title: data.title,
          content: data.content,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          userId: 1,
          viewCount: 0,
          answerCount: 0,
          user: mockUsers[0]
        }
        return mockResponse(newQuestion)
      }
      return request.post('/questions', data)
    },
    update: (id, data) => {
      if (useMockApi) {
        return mockResponse({ ...mockQuestions.find(q => q.id === Number(id)), ...data })
      }
      return request.put(`/questions/${id}`, data)
    },
    delete: (id) => {
      if (useMockApi) {
        return mockResponse({ success: true })
      }
      return request.delete(`/questions/${id}`)
    },
    search: (params) => {
      if (useMockApi) {
        return mockResponse({
          items: mockQuestions,
          total: mockQuestions.length
        })
      }
      return request.get('/questions/search', { params })
    }
  },

  // 答案相关
  answers: {
    list: (questionId, params) => request.get(`/questions/${questionId}/answers`, { params }),
    create: (questionId, data) => request.post(`/questions/${questionId}/answers`, data),
    update: (id, data) => request.put(`/answers/${id}`, data),
    delete: (id) => request.delete(`/answers/${id}`),
    accept: (id) => request.post(`/answers/${id}/accept`)
  },

  // 评论相关
  comments: {
    list: (contentType, contentId, params) => request.get(`/comments/${contentType}/${contentId}`, { params }),
    create: (data) => request.post('/comments', data),
    update: (id, data) => request.put(`/comments/${id}`, data),
    delete: (id) => request.delete(`/comments/${id}`)
  },

  // 分类相关
  categories: {
    list: () => request.get('/categories'),
    detail: (id) => request.get(`/categories/${id}`)
  },

  // 标签相关
  tags: {
    list: (params) => request.get('/tags', { params }),
    popular: () => request.get('/tags/popular')
  },

  // 资源相关
  resources: {
    list: (params) => request.get('/resources', { params }),
    detail: (id) => request.get(`/resources/${id}`),
    download: (id) => request.get(`/resources/${id}/download`, { responseType: 'blob' })
  },

  // 会员相关
  memberships: {
    packages: () => request.get('/memberships/packages'),
    current: () => request.get('/memberships/current'),
    purchase: (data) => request.post('/memberships/purchase', data)
  },

  // 订单相关
  orders: {
    list: (params) => request.get('/orders', { params }),
    detail: (id) => request.get(`/orders/${id}`),
    pay: (id, data) => request.post(`/orders/${id}/pay`, data)
  },

  // 通知相关
  notifications: {
    list: (params) => request.get('/notifications', { params }),
    markAsRead: (id) => request.put(`/notifications/${id}/read`),
    markAllAsRead: () => request.put('/notifications/read-all'),
    delete: (id) => request.delete(`/notifications/${id}`)
  },

  // 文件上传相关
  upload: {
    file: (file, onProgress) => {
      if (useMockApi) {
        return mockResponse({ url: 'https://via.placeholder.com/500' })
      }
      
      const formData = new FormData()
      formData.append('file', file)
      
      return request.post('/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: progressEvent => {
          if (onProgress && progressEvent.total) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
            onProgress(percentCompleted)
          }
        }
      })
    },
    
    multiple: (files, onProgress) => {
      if (useMockApi) {
        return mockResponse(
          Array.from({ length: files.length }).map(() => ({
            url: 'https://via.placeholder.com/500'
          }))
        )
      }
      
      const formData = new FormData()
      files.forEach(file => {
        formData.append('files', file)
      })
      
      return request.post('/upload/multiple', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: progressEvent => {
          if (onProgress && progressEvent.total) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
            onProgress(percentCompleted)
          }
        }
      })
    },
    
    delete: (filename) => {
      if (useMockApi) {
        return mockResponse(true)
      }
      
      return request.delete(`/upload/${filename}`)
    },
    
    image: (file, onProgress) => {
      return api.upload.file(file, onProgress)
    }
  },

  // 管理员相关
  admin: {
    dashboard: () => request.get('/admin/dashboard'),
    users: {
      list: (params) => request.get('/admin/users', { params }),
      detail: (id) => request.get(`/admin/users/${id}`),
      update: (id, data) => request.put(`/admin/users/${id}`, data),
      ban: (id) => request.post(`/admin/users/${id}/ban`),
      unban: (id) => request.post(`/admin/users/${id}/unban`)
    },
    questions: {
      list: (params) => request.get('/admin/questions', { params }),
      approve: (id) => request.post(`/admin/questions/${id}/approve`),
      reject: (id) => request.post(`/admin/questions/${id}/reject`),
      delete: (id) => request.delete(`/admin/questions/${id}`)
    },
    statistics: () => request.get('/admin/statistics')
  }
}

export default api
export { request }
