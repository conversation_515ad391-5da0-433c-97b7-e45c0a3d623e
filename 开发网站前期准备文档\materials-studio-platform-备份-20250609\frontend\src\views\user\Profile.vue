<template>
  <div class="profile-page">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>
    <div v-else-if="error" class="error-container">
      <el-alert
        :title="error"
        type="error"
        show-icon
        center
        :closable="false"
      />
      <div class="error-actions">
        <el-button type="primary" @click="fetchUserProfile">重试</el-button>
        <el-button @click="$router.push('/')">返回首页</el-button>
      </div>
    </div>
    <div v-else class="profile-content">
      <h1>个人中心</h1>
      
      <el-card class="profile-card">
        <div class="user-header">
          <el-avatar :size="80" :src="userProfile.avatar">
            {{ userInfo.username?.charAt(0).toUpperCase() }}
          </el-avatar>
          <div class="user-info">
            <h2>{{ userInfo.username }}</h2>
            <div class="user-meta">
              <span v-if="userInfo.email">邮箱: {{ userInfo.email }}</span>
              <span v-if="userInfo.isMember" class="member-badge">
                会员
              </span>
            </div>
          </div>
        </div>
        
        <el-divider />
        
        <div class="profile-details">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="用户名">{{ userInfo.username }}</el-descriptions-item>
            <el-descriptions-item label="邮箱">{{ userInfo.email || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="会员状态">
              {{ userInfo.isMember ? '会员' : '非会员' }}
            </el-descriptions-item>
            <el-descriptions-item v-if="userInfo.isMember" label="会员到期时间">
              {{ userInfo.memberExpireDate || '永久' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        
        <div class="profile-actions">
          <el-button type="primary" @click="showEditProfileDialog = true">编辑资料</el-button>
          <el-button @click="showChangePasswordDialog = true">修改密码</el-button>
        </div>
      </el-card>
    </div>
    
    <!-- 编辑资料对话框 -->
    <el-dialog
      v-model="showEditProfileDialog"
      title="编辑个人资料"
      width="500px"
      @closed="resetEditForm"
    >
      <el-form 
        :model="editForm" 
        :rules="editRules" 
        ref="editFormRef" 
        label-width="100px"
        status-icon
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="editForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="editForm.realName" placeholder="请输入真实姓名（选填）" />
        </el-form-item>
        <el-form-item label="头像">
          <el-upload
            class="avatar-uploader"
            action="#"
            :http-request="uploadAvatar"
            :show-file-list="false"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="editForm.avatar" :src="editForm.avatar" class="avatar-preview" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="avatar-tip">点击上传头像，支持 JPG、PNG 格式</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditProfileDialog = false">取消</el-button>
          <el-button type="primary" @click="submitEditProfile" :loading="submitting">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="showChangePasswordDialog"
      title="修改密码"
      width="500px"
      @closed="resetPasswordForm"
    >
      <el-form 
        :model="passwordForm" 
        :rules="passwordRules" 
        ref="passwordFormRef" 
        label-width="100px"
        status-icon
      >
        <el-form-item label="当前密码" prop="oldPassword">
          <el-input 
            v-model="passwordForm.oldPassword" 
            type="password"
            placeholder="请输入当前密码" 
            show-password
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input 
            v-model="passwordForm.newPassword" 
            type="password"
            placeholder="请输入新密码" 
            show-password
          />
        </el-form-item>
        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input 
            v-model="passwordForm.confirmPassword" 
            type="password"
            placeholder="请再次输入新密码" 
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showChangePasswordDialog = false">取消</el-button>
          <el-button type="primary" @click="submitChangePassword" :loading="submitting">
            修改
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import api from '@/api'

const router = useRouter()
const authStore = useAuthStore()
const userInfo = computed(() => authStore.userInfo)

// 页面状态
const loading = ref(true)
const error = ref('')
const userProfile = ref({})
const submitting = ref(false)

// 对话框状态
const showEditProfileDialog = ref(false)
const showChangePasswordDialog = ref(false)

// 表单引用
const editFormRef = ref(null)
const passwordFormRef = ref(null)

// 编辑资料表单
const editForm = reactive({
  username: '',
  email: '',
  realName: '',
  avatar: ''
})

// 编辑资料验证规则
const editRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度应为3-20个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 修改密码表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 修改密码验证规则
const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少为6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 获取用户个人资料
const fetchUserProfile = async () => {
  try {
    loading.value = true
    error.value = ''
    
    // 先检查认证状态
    if (!authStore.isAuthenticated) {
      throw new Error('请先登录')
    }
    
    // 获取用户信息
    await authStore.fetchUserInfo()
    
    // 这里可以添加获取更详细个人资料的API调用
    // 如果API尚未实现，可以先使用authStore中的用户信息
    userProfile.value = {
      ...userInfo.value,
      avatar: userInfo.value.avatar || ''
    }
  } catch (err) {
    console.error('获取用户资料失败:', err)
    error.value = err.message || '获取用户资料失败，请重试'
  } finally {
    loading.value = false
  }
}

// 初始化编辑表单数据
const initEditForm = () => {
  Object.assign(editForm, {
    username: userInfo.value.username || '',
    email: userInfo.value.email || '',
    realName: userInfo.value.realName || '',
    avatar: userInfo.value.avatar || ''
  })
}

// 重置编辑表单
const resetEditForm = () => {
  if (editFormRef.value) {
    editFormRef.value.resetFields()
  }
}

// 重置密码表单
const resetPasswordForm = () => {
  if (passwordFormRef.value) {
    passwordFormRef.value.resetFields()
  }
}

// 头像上传前的验证
const beforeAvatarUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJpgOrPng) {
    ElMessage.error('头像只能是 JPG 或 PNG 格式!')
  }
  
  if (!isLt2M) {
    ElMessage.error('头像大小不能超过 2MB!')
  }
  
  return isJpgOrPng && isLt2M
}

// 上传头像
const uploadAvatar = async (options) => {
  try {
    // 实际项目中应该调用API上传文件
    // 这里使用FileReader模拟上传效果
    const reader = new FileReader()
    reader.readAsDataURL(options.file)
    reader.onload = (e) => {
      editForm.avatar = e.target.result
    }
    
    // 成功回调
    if (options.onSuccess) {
      options.onSuccess()
    }
  } catch (error) {
    console.error('上传头像失败:', error)
    ElMessage.error('上传头像失败')
    
    // 失败回调
    if (options.onError) {
      options.onError()
    }
  }
}

// 提交编辑资料
const submitEditProfile = async () => {
  if (!editFormRef.value) return
  
  await editFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    try {
      submitting.value = true
      
      // TODO: 调用更新用户资料API
      // 这里模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 更新本地用户信息
      authStore.updateUserInfo({
        username: editForm.username,
        email: editForm.email,
        realName: editForm.realName,
        avatar: editForm.avatar
      })
      
      // 更新视图数据
      userProfile.value = {
        ...userProfile.value,
        ...editForm
      }
      
      ElMessage.success('个人资料更新成功')
      showEditProfileDialog.value = false
    } catch (err) {
      console.error('更新资料失败:', err)
      ElMessage.error(err.message || '更新资料失败，请重试')
    } finally {
      submitting.value = false
    }
  })
}

// 提交修改密码
const submitChangePassword = async () => {
  if (!passwordFormRef.value) return
  
  await passwordFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    try {
      submitting.value = true
      
      // TODO: 调用修改密码API
      // 这里模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      ElMessage.success('密码修改成功，请重新登录')
      
      // 密码修改成功后，退出登录
      await authStore.logout()
      router.push('/login')
    } catch (err) {
      console.error('修改密码失败:', err)
      ElMessage.error(err.message || '修改密码失败，请重试')
    } finally {
      submitting.value = false
    }
  })
}

// 监听编辑资料对话框打开
watch(showEditProfileDialog, (val) => {
  if (val) {
    initEditForm()
  }
})

onMounted(() => {
  fetchUserProfile()
})
</script>

<style scoped>
.profile-page {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
}

.loading-container {
  padding: 40px 0;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;
}

.error-actions {
  margin-top: 20px;
}

.profile-card {
  margin-top: 20px;
}

.user-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

.user-info {
  flex: 1;
}

.user-info h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
}

.user-meta {
  display: flex;
  gap: 16px;
  color: #666;
}

.member-badge {
  color: #e6a23c;
  font-weight: bold;
}

.profile-details {
  margin: 20px 0;
}

.profile-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

/* 头像上传相关样式 */
.avatar-uploader {
  width: 100px;
  height: 100px;
  border: 1px dashed var(--el-border-color);
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-tip {
  color: var(--el-text-color-secondary);
  font-size: 12px;
  margin-top: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
