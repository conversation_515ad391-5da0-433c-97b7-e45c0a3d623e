<template>
  <div class="data-analysis-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="page-header">
          <h1>材料数据分析</h1>
          <p>交互式可视化分析与材料性能比较</p>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="analysis-options" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>分析配置</span>
              <el-button-group>
                <el-button type="primary" @click="generateCharts" :disabled="selectedMaterials.length === 0">
                  生成分析
                </el-button>
                <el-button type="success" @click="exportData" :disabled="!analysisGenerated">
                  导出数据
                </el-button>
              </el-button-group>
            </div>
          </template>
          
          <el-form :model="analysisForm" label-width="100px" label-position="left">
            <el-row :gutter="20">
              <el-col :xs="24" :md="12">
                <el-form-item label="选择材料">
                  <el-select
                    v-model="selectedMaterials"
                    multiple
                    filterable
                    placeholder="请选择要分析的材料"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="material in availableMaterials"
                      :key="material.id"
                      :label="`${material.formula} - ${material.name}`"
                      :value="material.id"
                    >
                      <div style="display: flex; justify-content: space-between; align-items: center">
                        <span>{{ material.formula }}</span>
                        <span style="color: #8492a6; font-size: 13px">{{ material.name }}</span>
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              
              <el-col :xs="24" :md="12">
                <el-form-item label="分析类型">
                  <el-select v-model="analysisForm.type" placeholder="选择分析类型" style="width: 100%">
                    <el-option label="性能比较" value="performance"></el-option>
                    <el-option label="结构相关性" value="structure"></el-option>
                    <el-option label="热力学分析" value="thermodynamics"></el-option>
                    <el-option label="电子性质" value="electronic"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :xs="24" :md="12">
                <el-form-item label="图表类型">
                  <el-select v-model="analysisForm.chartType" placeholder="选择图表类型" style="width: 100%">
                    <el-option label="柱状图" value="bar"></el-option>
                    <el-option label="雷达图" value="radar"></el-option>
                    <el-option label="散点图" value="scatter"></el-option>
                    <el-option label="热图" value="heatmap"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              
              <el-col :xs="24" :md="12">
                <el-form-item label="数据维度">
                  <el-select
                    v-model="analysisForm.properties"
                    multiple
                    placeholder="选择要比较的属性"
                    style="width: 100%"
                  >
                    <el-option label="带隙 (eV)" value="bandGap"></el-option>
                    <el-option label="形成能 (eV/atom)" value="formationEnergy"></el-option>
                    <el-option label="体积模量 (GPa)" value="bulkModulus"></el-option>
                    <el-option label="剪切模量 (GPa)" value="shearModulus"></el-option>
                    <el-option label="热膨胀系数 (K⁻¹)" value="thermalExpansion"></el-option>
                    <el-option label="密度 (g/cm³)" value="density"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <!-- 根据分析类型显示不同的附加选项 -->
            <div v-if="analysisForm.type === 'structure'">
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="结构参数">
                    <el-checkbox-group v-model="analysisForm.structureParams">
                      <el-checkbox label="latticeParams">晶格常数</el-checkbox>
                      <el-checkbox label="bondLengths">键长分布</el-checkbox>
                      <el-checkbox label="coordination">配位数</el-checkbox>
                      <el-checkbox label="symmetry">对称性</el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            
            <div v-if="analysisForm.type === 'thermodynamics'">
              <el-row :gutter="20">
                <el-col :xs="24" :md="12">
                  <el-form-item label="温度范围">
                    <div style="display: flex; align-items: center;">
                      <el-input-number v-model="analysisForm.tempMin" :min="0" :max="2000"></el-input-number>
                      <span style="margin: 0 10px;">-</span>
                      <el-input-number v-model="analysisForm.tempMax" :min="0" :max="2000"></el-input-number>
                      <span style="margin-left: 10px;">K</span>
                    </div>
                  </el-form-item>
                </el-col>
                
                <el-col :xs="24" :md="12">
                  <el-form-item label="压力条件">
                    <el-select v-model="analysisForm.pressure" placeholder="选择压力条件" style="width: 100%">
                      <el-option label="标准大气压 (1 atm)" value="1"></el-option>
                      <el-option label="高压 (10 GPa)" value="10"></el-option>
                      <el-option label="超高压 (100 GPa)" value="100"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 分析结果展示 -->
    <el-row :gutter="20" class="analysis-results" v-if="analysisGenerated">
      <el-col :span="24">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>分析结果</span>
              <el-radio-group v-model="displayMode" size="small">
                <el-radio-button label="charts">图表模式</el-radio-button>
                <el-radio-button label="table">表格模式</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          
          <!-- 图表模式 -->
          <div v-if="displayMode === 'charts'" class="charts-container">
            <el-row :gutter="20">
              <!-- 主图表 -->
              <el-col :xs="24" :lg="16">
                <div class="main-chart">
                  <div class="chart-wrapper">
                    <div ref="mainChart" style="width: 100%; height: 400px;"></div>
                  </div>
                </div>
              </el-col>
              
              <!-- 属性详情 -->
              <el-col :xs="24" :lg="8">
                <div class="property-details">
                  <h3>材料属性详情</h3>
                  <el-table :data="materialDetails" style="width: 100%">
                    <el-table-column prop="property" label="属性" width="180"></el-table-column>
                    <el-table-column prop="value" label="值"></el-table-column>
                  </el-table>
                  
                  <div class="selected-material" v-if="selectedMaterialId">
                    <h4>{{ selectedMaterialName }}</h4>
                    <p>{{ selectedMaterialDescription }}</p>
                    <el-button type="text" @click="viewMaterialDetails">查看完整详情</el-button>
                  </div>
                </div>
              </el-col>
            </el-row>
            
            <!-- 相关性分析 -->
            <div class="correlation-analysis" v-if="analysisForm.properties.length > 1">
              <h3>属性相关性分析</h3>
              <el-row :gutter="20">
                <el-col :xs="24" :md="12">
                  <div class="chart-wrapper">
                    <div ref="correlationChart" style="width: 100%; height: 300px;"></div>
                  </div>
                </el-col>
                <el-col :xs="24" :md="12">
                  <div class="correlation-explanation">
                    <h4>主要发现</h4>
                    <ul>
                      <li>
                        <strong>强正相关 (r > 0.7)：</strong>
                        <span v-if="correlations.positive.length">{{ correlations.positive.join(', ') }}</span>
                        <span v-else>未发现</span>
                      </li>
                      <li>
                        <strong>强负相关 (r < -0.7)：</strong>
                        <span v-if="correlations.negative.length">{{ correlations.negative.join(', ') }}</span>
                        <span v-else>未发现</span>
                      </li>
                      <li>
                        <strong>无明显相关性 (-0.3 < r < 0.3)：</strong>
                        <span v-if="correlations.none.length">{{ correlations.none.join(', ') }}</span>
                        <span v-else>未发现</span>
                      </li>
                    </ul>
                    <p class="correlation-note">
                      相关性分析基于当前选择的材料样本，结果可能随样本变化而改变。
                    </p>
                  </div>
                </el-col>
              </el-row>
            </div>
            
            <!-- 补充图表 -->
            <div class="supplementary-charts" v-if="analysisForm.type === 'electronic'">
              <h3>电子结构分析</h3>
              <el-row :gutter="20">
                <el-col :xs="24" :md="12">
                  <div class="chart-wrapper">
                    <div ref="dosChart" style="width: 100%; height: 300px;"></div>
                    <div class="chart-title">态密度(DOS)对比</div>
                  </div>
                </el-col>
                <el-col :xs="24" :md="12">
                  <div class="chart-wrapper">
                    <div ref="bandStructureChart" style="width: 100%; height: 300px;"></div>
                    <div class="chart-title">能带结构对比</div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
          
          <!-- 表格模式 -->
          <div v-if="displayMode === 'table'" class="table-container">
            <el-table
              :data="tableData"
              style="width: 100%"
              border
              stripe
              :default-sort="{prop: 'formula', order: 'ascending'}"
            >
              <el-table-column prop="formula" label="化学式" sortable></el-table-column>
              <el-table-column prop="name" label="材料名称" sortable></el-table-column>
              <el-table-column
                v-for="prop in analysisForm.properties"
                :key="prop"
                :prop="prop"
                :label="getPropertyLabel(prop)"
                sortable
              ></el-table-column>
              <el-table-column label="操作" width="150">
                <template #default="scope">
                  <el-button type="text" size="small" @click="selectMaterial(scope.row.id)">查看详情</el-button>
                  <el-button type="text" size="small" @click="removeMaterial(scope.row.id)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <div class="export-options">
              <el-button type="primary" size="small" @click="exportTable('csv')">导出CSV</el-button>
              <el-button type="primary" size="small" @click="exportTable('excel')">导出Excel</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 推荐分析模板 -->
    <el-row :gutter="20" class="analysis-templates" v-if="!analysisGenerated">
      <el-col :span="24">
        <h2 class="section-title">推荐分析模板</h2>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="template in analysisTemplates" :key="template.id">
        <el-card shadow="hover" class="template-card" @click.native="applyTemplate(template)">
          <div class="template-icon">
            <i :class="template.icon"></i>
          </div>
          <h3>{{ template.title }}</h3>
          <p>{{ template.description }}</p>
          <div class="template-tags">
            <el-tag size="mini" v-for="tag in template.tags" :key="tag" effect="plain">{{ tag }}</el-tag>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
// 假设使用ECharts作为图表库
// import * as echarts from 'echarts/core'

export default {
  name: 'DataAnalysisView',
  setup() {
    const router = useRouter()
    
    // 分析表单数据
    const analysisForm = reactive({
      type: 'performance',
      chartType: 'bar',
      properties: ['bandGap', 'formationEnergy', 'density'],
      structureParams: ['latticeParams', 'bondLengths'],
      tempMin: 300,
      tempMax: 1000,
      pressure: '1'
    })
    
    // 材料数据
    const availableMaterials = ref([
      { id: '1', formula: 'SiO2', name: '二氧化硅', description: '二氧化硅是地壳中最丰富的矿物之一，广泛应用于玻璃、陶瓷和半导体领域。' },
      { id: '2', formula: 'TiO2', name: '二氧化钛', description: '二氧化钛是一种重要的功能材料，具有优异的光催化性能和生物相容性。' },
      { id: '3', formula: 'Al2O3', name: '氧化铝', description: '氧化铝是一种高硬度陶瓷材料，广泛用于耐火材料、催化剂载体和电子基板。' },
      { id: '4', formula: 'Fe3O4', name: '四氧化三铁', description: '四氧化三铁是一种铁磁性材料，具有良好的电磁特性，应用于磁存储和传感器。' },
      { id: '5', formula: 'ZnO', name: '氧化锌', description: '氧化锌是一种多功能半导体材料，在光电、传感器和催化领域有广泛应用。' },
      { id: '6', formula: 'GaN', name: '氮化镓', description: '氮化镓是一种宽禁带半导体，在高功率电子器件和蓝光LED中发挥重要作用。' },
      { id: '7', formula: 'CdTe', name: '碲化镉', description: '碲化镉是一种直接带隙半导体，主要用于太阳能电池和红外探测器。' },
      { id: '8', formula: 'Cu2O', name: '氧化亚铜', description: '氧化亚铜是一种p型半导体，在太阳能转换和光催化应用中具有潜力。' }
    ])
    
    // 材料属性数据 (实际应用中应从API获取)
    const materialProperties = reactive({
      '1': { bandGap: 8.9, formationEnergy: -9.6, bulkModulus: 36.8, shearModulus: 31.1, thermalExpansion: 0.55e-6, density: 2.65 },
      '2': { bandGap: 3.2, formationEnergy: -9.8, bulkModulus: 210, shearModulus: 112, thermalExpansion: 9.0e-6, density: 4.23 },
      '3': { bandGap: 8.8, formationEnergy: -17.3, bulkModulus: 240, shearModulus: 150, thermalExpansion: 8.1e-6, density: 3.95 },
      '4': { bandGap: 0.1, formationEnergy: -5.9, bulkModulus: 186, shearModulus: 80, thermalExpansion: 9.2e-6, density: 5.17 },
      '5': { bandGap: 3.3, formationEnergy: -3.6, bulkModulus: 143, shearModulus: 45, thermalExpansion: 2.9e-6, density: 5.61 },
      '6': { bandGap: 3.4, formationEnergy: -1.3, bulkModulus: 210, shearModulus: 123, thermalExpansion: 3.2e-6, density: 6.15 },
      '7': { bandGap: 1.5, formationEnergy: -1.5, bulkModulus: 42, shearModulus: 18, thermalExpansion: 4.9e-6, density: 5.85 },
      '8': { bandGap: 2.1, formationEnergy: -1.8, bulkModulus: 106, shearModulus: 10, thermalExpansion: 1.1e-6, density: 6.0 }
    })
    
    // 状态变量
    const selectedMaterials = ref(['1', '2', '3'])
    const analysisGenerated = ref(false)
    const displayMode = ref('charts')
    const selectedMaterialId = ref(null)
    const materialDetails = ref([])
    
    // 图表相关引用
    const mainChart = ref(null)
    const correlationChart = ref(null)
    const dosChart = ref(null)
    const bandStructureChart = ref(null)
    
    // 模拟相关性分析结果
    const correlations = reactive({
      positive: ['带隙 vs 形成能'],
      negative: ['密度 vs 热膨胀系数'],
      none: ['带隙 vs 密度']
    })
    
    // 计算属性
    const selectedMaterialName = computed(() => {
      if (!selectedMaterialId.value) return ''
      const material = availableMaterials.value.find(m => m.id === selectedMaterialId.value)
      return material ? `${material.formula} - ${material.name}` : ''
    })
    
    const selectedMaterialDescription = computed(() => {
      if (!selectedMaterialId.value) return ''
      const material = availableMaterials.value.find(m => m.id === selectedMaterialId.value)
      return material ? material.description : ''
    })
    
    // 表格数据
    const tableData = computed(() => {
      return selectedMaterials.value.map(id => {
        const material = availableMaterials.value.find(m => m.id === id)
        const properties = materialProperties[id]
        return {
          id,
          formula: material.formula,
          name: material.name,
          ...properties
        }
      })
    })
    
    // 分析模板
    const analysisTemplates = ref([
      {
        id: 1,
        title: '半导体材料比较',
        description: '比较常见半导体材料的带隙、载流子迁移率和热导率',
        icon: 'el-icon-cpu',
        tags: ['半导体', '电子性质', '带隙分析'],
        config: {
          type: 'electronic',
          chartType: 'bar',
          properties: ['bandGap', 'formationEnergy', 'density'],
          preselectedMaterials: ['5', '6', '7']
        }
      },
      {
        id: 2,
        title: '氧化物结构关系',
        description: '分析常见金属氧化物的结构参数和性能关系',
        icon: 'el-icon-connection',
        tags: ['氧化物', '结构分析', '性能相关性'],
        config: {
          type: 'structure',
          chartType: 'radar',
          properties: ['bulkModulus', 'shearModulus', 'density'],
          structureParams: ['latticeParams', 'coordination'],
          preselectedMaterials: ['1', '2', '3', '5']
        }
      },
      {
        id: 3,
        title: '热力学稳定性分析',
        description: '比较不同材料的形成能和热膨胀特性',
        icon: 'el-icon-data-line',
        tags: ['热力学', '稳定性', '形成能'],
        config: {
          type: 'thermodynamics',
          chartType: 'scatter',
          properties: ['formationEnergy', 'thermalExpansion'],
          tempMin: 300,
          tempMax: 1200,
          pressure: '1',
          preselectedMaterials: ['1', '2', '3', '4']
        }
      },
      {
        id: 4,
        title: '机械性能对比',
        description: '分析材料的体积模量、剪切模量和强度特性',
        icon: 'el-icon-medal',
        tags: ['机械性能', '弹性模量', '强度分析'],
        config: {
          type: 'performance',
          chartType: 'radar',
          properties: ['bulkModulus', 'shearModulus', 'density'],
          preselectedMaterials: ['1', '2', '3', '4']
        }
      }
    ])
    
    // 方法
    const generateCharts = () => {
      analysisGenerated.value = true
      // 在实际应用中，这里会使用图表库创建图表
      console.log('生成分析图表', analysisForm)
      // 模拟选中第一个材料
      if (selectedMaterials.value.length > 0) {
        selectMaterial(selectedMaterials.value[0])
      }
    }
    
    const exportData = () => {
      console.log('导出数据')
      // 实际项目中会导出数据到文件
    }
    
    const selectMaterial = (id) => {
      selectedMaterialId.value = id
      const material = availableMaterials.value.find(m => m.id === id)
      const properties = materialProperties[id]
      
      // 更新材料详情
      materialDetails.value = [
        { property: '化学式', value: material.formula },
        { property: '名称', value: material.name }
      ]
      
      // 添加选中的属性
      analysisForm.properties.forEach(prop => {
        materialDetails.value.push({
          property: getPropertyLabel(prop),
          value: properties[prop]
        })
      })
    }
    
    const removeMaterial = (id) => {
      const index = selectedMaterials.value.indexOf(id)
      if (index !== -1) {
        selectedMaterials.value.splice(index, 1)
      }
      if (selectedMaterialId.value === id) {
        selectedMaterialId.value = null
      }
    }
    
    const viewMaterialDetails = () => {
      if (selectedMaterialId.value) {
        router.push(`/resources/${selectedMaterialId.value}`)
      }
    }
    
    const exportTable = (format) => {
      console.log(`导出表格为${format}格式`)
      // 实际项目中会导出表格数据
    }
    
    const applyTemplate = (template) => {
      // 应用模板配置
      Object.assign(analysisForm, template.config)
      selectedMaterials.value = [...template.config.preselectedMaterials]
      generateCharts()
    }
    
    const resetFilters = () => {
      Object.assign(analysisForm, {
        type: 'performance',
        chartType: 'bar',
        properties: ['bandGap', 'formationEnergy', 'density'],
        structureParams: ['latticeParams', 'bondLengths'],
        tempMin: 300,
        tempMax: 1000,
        pressure: '1'
      })
      selectedMaterials.value = []
    }
    
    const getPropertyLabel = (prop) => {
      const labels = {
        bandGap: '带隙 (eV)',
        formationEnergy: '形成能 (eV/atom)',
        bulkModulus: '体积模量 (GPa)',
        shearModulus: '剪切模量 (GPa)',
        thermalExpansion: '热膨胀系数 (K⁻¹)',
        density: '密度 (g/cm³)'
      }
      return labels[prop] || prop
    }
    
    // 监听选择变化
    watch(selectedMaterials, (newVal) => {
      // 如果没有选中的材料，重置分析结果
      if (newVal.length === 0) {
        analysisGenerated.value = false
      }
    })
    
    onMounted(() => {
      console.log('数据分析组件已加载')
      // 在实际项目中，这里会初始化图表库
    })
    
    return {
      analysisForm,
      availableMaterials,
      selectedMaterials,
      analysisGenerated,
      displayMode,
      selectedMaterialId,
      materialDetails,
      correlations,
      tableData,
      analysisTemplates,
      mainChart,
      correlationChart,
      dosChart,
      bandStructureChart,
      selectedMaterialName,
      selectedMaterialDescription,
      generateCharts,
      exportData,
      selectMaterial,
      removeMaterial,
      viewMaterialDetails,
      exportTable,
      applyTemplate,
      resetFilters,
      getPropertyLabel
    }
  }
}
</script>

<style scoped lang="scss">
.data-analysis-container {
  padding: 20px;
  
  .page-header {
    text-align: center;
    margin-bottom: 30px;
    
    h1 {
      font-size: 28px;
      font-weight: 600;
      margin-bottom: 10px;
    }
    
    p {
      color: #666;
      font-size: 16px;
    }
  }
  
  .analysis-options {
    margin-bottom: 30px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .analysis-results {
    margin-bottom: 30px;
    
    .charts-container {
      .main-chart {
        margin-bottom: 30px;
      }
      
      .chart-wrapper {
        background-color: #f8f8f8;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 15px;
        
        .chart-title {
          text-align: center;
          margin-top: 10px;
          font-size: 14px;
          color: #666;
        }
      }
      
      .property-details {
        padding: 0 15px;
        
        h3 {
          margin-top: 0;
          margin-bottom: 15px;
          font-size: 18px;
          font-weight: 600;
        }
        
        .selected-material {
          margin-top: 20px;
          background-color: #f8f8f8;
          padding: 15px;
          border-radius: 4px;
          
          h4 {
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: 600;
          }
          
          p {
            margin-bottom: 15px;
            font-size: 14px;
            line-height: 1.5;
            color: #666;
          }
        }
      }
      
      .correlation-analysis {
        margin-top: 30px;
        
        h3 {
          margin-bottom: 15px;
          font-size: 18px;
          font-weight: 600;
        }
        
        .correlation-explanation {
          padding: 15px;
          
          h4 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 16px;
            font-weight: 600;
          }
          
          ul {
            padding-left: 20px;
            margin-bottom: 15px;
            
            li {
              margin-bottom: 10px;
              font-size: 14px;
              line-height: 1.5;
            }
          }
          
          .correlation-note {
            font-size: 13px;
            color: #999;
            font-style: italic;
          }
        }
      }
      
      .supplementary-charts {
        margin-top: 30px;
        
        h3 {
          margin-bottom: 15px;
          font-size: 18px;
          font-weight: 600;
        }
      }
    }
    
    .table-container {
      .export-options {
        margin-top: 15px;
        text-align: right;
        
        .el-button {
          margin-left: 10px;
        }
      }
    }
  }
  
  .analysis-templates {
    .section-title {
      font-size: 22px;
      font-weight: 600;
      margin-bottom: 20px;
      text-align: center;
    }
    
    .template-card {
      margin-bottom: 20px;
      cursor: pointer;
      transition: transform 0.3s;
      
      &:hover {
        transform: translateY(-5px);
      }
      
      .template-icon {
        font-size: 40px;
        margin-bottom: 15px;
        color: #409EFF;
        text-align: center;
      }
      
      h3 {
        margin: 10px 0;
        font-size: 18px;
        text-align: center;
      }
      
      p {
        color: #666;
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 15px;
        min-height: 60px;
      }
      
      .template-tags {
        margin-bottom: 5px;
        
        .el-tag {
          margin-right: 5px;
          margin-bottom: 5px;
        }
      }
    }
  }
}
</style> 