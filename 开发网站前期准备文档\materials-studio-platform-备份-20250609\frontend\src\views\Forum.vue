<template>
  <div class="forum-container">
    <div class="page-header">
      <h1>材料科学论坛</h1>
      <p>交流讨论，分享知识</p>
    </div>
    
    <el-row :gutter="20">
      <el-col :md="16" :xs="24">
        <el-card class="main-content" shadow="hover">
          <div class="forum-header">
            <div class="category-filter">
              <el-radio-group v-model="currentCategory" @change="handleCategoryChange">
                <el-radio-button label="all">全部讨论</el-radio-button>
                <el-radio-button label="materials">材料科学</el-radio-button>
                <el-radio-button label="computation">计算模拟</el-radio-button>
                <el-radio-button label="characterization">表征技术</el-radio-button>
                <el-radio-button label="application">应用技术</el-radio-button>
              </el-radio-group>
            </div>
            
            <div class="sort-control">
              <el-dropdown @command="handleSortChange" trigger="click">
                <span class="el-dropdown-link">
                  {{ getSortLabel(currentSort) }} <i class="el-icon-arrow-down el-icon--right"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="newest">最新发布</el-dropdown-item>
                  <el-dropdown-item command="popular">热门讨论</el-dropdown-item>
                  <el-dropdown-item command="replies">回复最多</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
          
          <div class="topic-list" v-loading="loading">
            <div 
              class="topic-item"
              v-for="topic in topics"
              :key="topic.id"
              @click="viewTopic(topic)"
            >
              <div class="topic-avatar">
                <el-avatar :size="50" :src="topic.author.avatar"></el-avatar>
              </div>
              
              <div class="topic-content">
                <div class="topic-title">
                  <span class="title-text">{{ topic.title }}</span>
                  <el-tag 
                    size="mini" 
                    :type="getCategoryTagType(topic.category)"
                    effect="plain"
                  >
                    {{ getCategoryName(topic.category) }}
                  </el-tag>
                  <el-tag 
                    v-if="topic.isSticky" 
                    size="mini" 
                    type="danger"
                    effect="dark"
                  >置顶</el-tag>
                </div>
                
                <div class="topic-meta">
                  <span class="author">{{ topic.author.name }}</span>
                  <span class="time">{{ formatDate(topic.createdAt) }}</span>
                </div>
                
                <div class="topic-summary">
                  {{ topic.summary }}
                </div>
                
                <div class="topic-stats">
                  <div class="stat-item">
                    <i class="el-icon-view"></i>
                    <span>{{ topic.views }}</span>
                  </div>
                  <div class="stat-item">
                    <i class="el-icon-chat-dot-square"></i>
                    <span>{{ topic.replies }}</span>
                  </div>
                  <div class="stat-item">
                    <i class="el-icon-star-off"></i>
                    <span>{{ topic.likes }}</span>
                  </div>
                </div>
              </div>
              
              <div class="topic-last-reply" v-if="topic.lastReply">
                <div class="last-reply-info">
                  <span class="last-reply-text">最后回复</span>
                  <span class="last-reply-time">{{ formatDate(topic.lastReply.createdAt) }}</span>
                </div>
                <el-avatar :size="30" :src="topic.lastReply.author.avatar"></el-avatar>
              </div>
            </div>
          </div>
          
          <div class="pagination-container">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="totalItems"
            ></el-pagination>
          </div>
        </el-card>
      </el-col>
      
      <el-col :md="8" :xs="24">
        <el-card class="sidebar-card" shadow="hover">
          <div class="sidebar-actions">
            <el-button 
              type="primary" 
              icon="el-icon-edit" 
              class="new-topic-btn"
              @click="createNewTopic"
            >
              发布新讨论
            </el-button>
            
            <div class="search-box">
              <el-input
                placeholder="搜索讨论话题"
                prefix-icon="el-icon-search"
                v-model="searchQuery"
                clearable
                @keyup.enter.native="searchTopics"
              ></el-input>
            </div>
          </div>
        </el-card>
        
        <el-card class="sidebar-card" shadow="hover">
          <div class="card-header">
            <h3>热门讨论</h3>
          </div>
          <div class="trending-topics">
            <div 
              class="trending-topic-item"
              v-for="topic in trendingTopics"
              :key="topic.id"
              @click="viewTopic(topic)"
            >
              <div class="trending-title">{{ topic.title }}</div>
              <div class="trending-meta">
                <span class="trending-stat">
                  <i class="el-icon-view"></i> {{ topic.views }}
                </span>
                <span class="trending-stat">
                  <i class="el-icon-chat-dot-square"></i> {{ topic.replies }}
                </span>
              </div>
            </div>
          </div>
        </el-card>
        
        <el-card class="sidebar-card" shadow="hover">
          <div class="card-header">
            <h3>活跃用户</h3>
          </div>
          <div class="active-users">
            <div class="active-user-item" v-for="user in activeUsers" :key="user.id">
              <el-avatar :size="40" :src="user.avatar"></el-avatar>
              <div class="active-user-info">
                <div class="active-user-name">{{ user.name }}</div>
                <div class="active-user-topics">{{ user.topicCount }} 篇讨论</div>
              </div>
              <div class="active-user-badge">
                <el-tag size="mini" type="success" effect="plain">活跃</el-tag>
              </div>
            </div>
          </div>
        </el-card>
        
        <el-card class="sidebar-card" shadow="hover">
          <div class="card-header">
            <h3>论坛统计</h3>
          </div>
          <div class="forum-stats">
            <div class="forum-stat-item">
              <div class="stat-title">讨论主题</div>
              <div class="stat-value">{{ forumStats.topics }}</div>
            </div>
            <div class="forum-stat-item">
              <div class="stat-title">回复数量</div>
              <div class="stat-value">{{ forumStats.replies }}</div>
            </div>
            <div class="forum-stat-item">
              <div class="stat-title">注册用户</div>
              <div class="stat-value">{{ forumStats.users }}</div>
            </div>
            <div class="forum-stat-item">
              <div class="stat-title">最近活跃</div>
              <div class="stat-value">{{ forumStats.recentActive }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 新话题对话框 -->
    <el-dialog 
      title="发布新讨论" 
      :visible.sync="newTopicDialogVisible"
      width="60%"
    >
      <el-form :model="newTopicForm" label-width="80px">
        <el-form-item label="标题" required>
          <el-input v-model="newTopicForm.title" placeholder="请输入讨论标题"></el-input>
        </el-form-item>
        <el-form-item label="分类" required>
          <el-select v-model="newTopicForm.category" placeholder="请选择分类">
            <el-option label="材料科学" value="materials"></el-option>
            <el-option label="计算模拟" value="computation"></el-option>
            <el-option label="表征技术" value="characterization"></el-option>
            <el-option label="应用技术" value="application"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="内容" required>
          <el-input 
            type="textarea" 
            :rows="8" 
            v-model="newTopicForm.content"
            placeholder="请输入讨论内容"
          ></el-input>
        </el-form-item>
        <el-form-item label="标签">
          <el-tag
            :key="tag"
            v-for="tag in newTopicForm.tags"
            closable
            :disable-transitions="false"
            @close="handleTagClose(tag)"
          >
            {{tag}}
          </el-tag>
          <el-input
            class="input-new-tag"
            v-if="inputTagVisible"
            v-model="inputTagValue"
            ref="saveTagInput"
            size="small"
            @keyup.enter.native="handleInputTagConfirm"
            @blur="handleInputTagConfirm"
          >
          </el-input>
          <el-button v-else class="button-new-tag" size="small" @click="showTagInput">+ 添加标签</el-button>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="newTopicDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitNewTopic">发布</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Forum',
  data() {
    return {
      loading: false,
      currentCategory: 'all',
      currentSort: 'newest',
      currentPage: 1,
      pageSize: 10,
      totalItems: 0,
      searchQuery: '',
      newTopicDialogVisible: false,
      inputTagVisible: false,
      inputTagValue: '',
      
      newTopicForm: {
        title: '',
        category: '',
        content: '',
        tags: []
      },
      
      // 模拟数据，实际应从API获取
      topics: [
        {
          id: 1,
          title: '新型高熵合金的结构稳定性讨论',
          category: 'materials',
          summary: '近期研究了几种新型高熵合金的相稳定性，发现温度对其微观结构有显著影响，想请教各位专家对这一现象的见解...',
          author: {
            id: 101,
            name: '李研究员',
            avatar: 'https://cube.elemecdn.com/3/7c/********************************.png'
          },
          createdAt: '2023-06-15T09:30:00',
          views: 328,
          replies: 24,
          likes: 46,
          isSticky: true,
          lastReply: {
            id: 201,
            content: '我认为这与高熵效应导致的元素扩散机制变化有关...',
            createdAt: '2023-06-17T14:25:00',
            author: {
              id: 105,
              name: '张教授',
              avatar: 'https://cube.elemecdn.com/3/7c/********************************.png'
            }
          }
        },
        {
          id: 2,
          title: '第一性原理计算在能源材料中的应用问题',
          category: 'computation',
          summary: '在使用VASP对锂电池电极材料进行计算时遇到了收敛问题，特别是在考虑带电结构时，有什么好的解决方案？',
          author: {
            id: 102,
            name: '王博士',
            avatar: 'https://cube.elemecdn.com/3/7c/********************************.png'
          },
          createdAt: '2023-06-14T16:45:00',
          views: 235,
          replies: 18,
          likes: 27,
          isSticky: false,
          lastReply: {
            id: 202,
            content: '建议尝试调整INCAR中的NELM和EDIFF参数，有时加入ALGO=Normal也有帮助...',
            createdAt: '2023-06-16T10:20:00',
            author: {
              id: 103,
              name: '陈研究员',
              avatar: 'https://cube.elemecdn.com/3/7c/********************************.png'
            }
          }
        },
        {
          id: 3,
          title: 'SEM表征技术中样品制备的最佳实践',
          category: 'characterization',
          summary: '针对不同类型的材料，SEM样品制备有哪些需要注意的事项？特别是对于易氧化的金属材料，有什么好的保存方法？',
          author: {
            id: 103,
            name: '陈研究员',
            avatar: 'https://cube.elemecdn.com/3/7c/********************************.png'
          },
          createdAt: '2023-06-13T11:20:00',
          views: 187,
          replies: 15,
          likes: 32,
          isSticky: false,
          lastReply: {
            id: 203,
            content: '对于易氧化金属，我们实验室通常使用高纯氩气保存环境，并在使用前用离子束清洁表面...',
            createdAt: '2023-06-15T09:15:00',
            author: {
              id: 104,
              name: '刘工程师',
              avatar: 'https://cube.elemecdn.com/3/7c/********************************.png'
            }
          }
        },
        {
          id: 4,
          title: '新型光电材料在柔性电子中的应用前景',
          category: 'application',
          summary: '近年来有不少新型光电材料被开发出来，大家觉得哪些材料在柔性电子领域最有应用前景？目前存在哪些技术瓶颈？',
          author: {
            id: 105,
            name: '张教授',
            avatar: 'https://cube.elemecdn.com/3/7c/********************************.png'
          },
          createdAt: '2023-06-12T14:30:00',
          views: 312,
          replies: 28,
          likes: 53,
          isSticky: true,
          lastReply: {
            id: 204,
            content: '我认为有机-无机杂化钙钛矿材料很有前景，但稳定性和大规模制备仍是主要挑战...',
            createdAt: '2023-06-17T08:40:00',
            author: {
              id: 101,
              name: '李研究员',
              avatar: 'https://cube.elemecdn.com/3/7c/********************************.png'
            }
          }
        },
        {
          id: 5,
          title: '材料数据库中机器学习算法的选择问题',
          category: 'computation',
          summary: '在使用材料数据库进行性能预测时，针对不同类型的材料性质，有什么推荐的机器学习算法和特征选择方法？',
          author: {
            id: 106,
            name: '赵博士',
            avatar: 'https://cube.elemecdn.com/3/7c/********************************.png'
          },
          createdAt: '2023-06-10T09:15:00',
          views: 245,
          replies: 21,
          likes: 38,
          isSticky: false,
          lastReply: {
            id: 205,
            content: '对于结构-性能关系的预测，我们团队发现基于图神经网络的方法效果很好，特别是...',
            createdAt: '2023-06-16T15:30:00',
            author: {
              id: 102,
              name: '王博士',
              avatar: 'https://cube.elemecdn.com/3/7c/********************************.png'
            }
          }
        }
      ],
      
      trendingTopics: [
        {
          id: 4,
          title: '新型光电材料在柔性电子中的应用前景',
          views: 312,
          replies: 28
        },
        {
          id: 1,
          title: '新型高熵合金的结构稳定性讨论',
          views: 328,
          replies: 24
        },
        {
          id: 6,
          title: '纳米材料表征中的常见误区分析',
          views: 198,
          replies: 17
        },
        {
          id: 7,
          title: '先进陶瓷材料的高温力学性能测试方法',
          views: 176,
          replies: 14
        },
        {
          id: 8,
          title: '可降解生物材料的体外评价标准',
          views: 165,
          replies: 12
        }
      ],
      
      activeUsers: [
        {
          id: 105,
          name: '张教授',
          avatar: 'https://cube.elemecdn.com/3/7c/********************************.png',
          topicCount: 32
        },
        {
          id: 101,
          name: '李研究员',
          avatar: 'https://cube.elemecdn.com/3/7c/********************************.png',
          topicCount: 28
        },
        {
          id: 106,
          name: '赵博士',
          avatar: 'https://cube.elemecdn.com/3/7c/********************************.png',
          topicCount: 25
        },
        {
          id: 102,
          name: '王博士',
          avatar: 'https://cube.elemecdn.com/3/7c/********************************.png',
          topicCount: 23
        },
        {
          id: 103,
          name: '陈研究员',
          avatar: 'https://cube.elemecdn.com/3/7c/********************************.png',
          topicCount: 19
        }
      ],
      
      forumStats: {
        topics: 1284,
        replies: 15729,
        users: 4352,
        recentActive: 368
      }
    }
  },
  created() {
    this.fetchTopics();
  },
  methods: {
    fetchTopics() {
      this.loading = true;
      
      // 模拟API请求延迟
      setTimeout(() => {
        // 在实际项目中，这里会是真实的API调用
        // this.topics = response.data.topics;
        // this.totalItems = response.data.total;
        this.totalItems = this.topics.length;
        this.loading = false;
      }, 500);
    },
    
    handleCategoryChange(category) {
      this.currentPage = 1;
      this.fetchTopics();
    },
    
    handleSortChange(sort) {
      this.currentSort = sort;
      this.currentPage = 1;
      this.fetchTopics();
    },
    
    getSortLabel(sort) {
      const labels = {
        'newest': '最新发布',
        'popular': '热门讨论',
        'replies': '回复最多'
      };
      return labels[sort] || '排序';
    },
    
    handleSizeChange(size) {
      this.pageSize = size;
      this.fetchTopics();
    },
    
    handleCurrentChange(page) {
      this.currentPage = page;
      this.fetchTopics();
    },
    
    viewTopic(topic) {
      this.$router.push(`/forum/topic/${topic.id}`);
    },
    
    searchTopics() {
      if (this.searchQuery.trim()) {
        this.currentPage = 1;
        this.fetchTopics();
      }
    },
    
    formatDate(dateString) {
      const now = new Date();
      const date = new Date(dateString);
      const diff = Math.floor((now - date) / 1000); // 秒数差
      
      if (diff < 60) {
        return '刚刚';
      } else if (diff < 3600) {
        return Math.floor(diff / 60) + '分钟前';
      } else if (diff < 86400) {
        return Math.floor(diff / 3600) + '小时前';
      } else if (diff < 604800) {
        return Math.floor(diff / 86400) + '天前';
      } else {
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      }
    },
    
    getCategoryName(category) {
      const categories = {
        'materials': '材料科学',
        'computation': '计算模拟',
        'characterization': '表征技术',
        'application': '应用技术'
      };
      return categories[category] || category;
    },
    
    getCategoryTagType(category) {
      const types = {
        'materials': '',
        'computation': 'success',
        'characterization': 'warning',
        'application': 'info'
      };
      return types[category] || '';
    },
    
    createNewTopic() {
      this.newTopicForm = {
        title: '',
        category: '',
        content: '',
        tags: []
      };
      this.newTopicDialogVisible = true;
    },
    
    showTagInput() {
      this.inputTagVisible = true;
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    
    handleTagClose(tag) {
      this.newTopicForm.tags.splice(this.newTopicForm.tags.indexOf(tag), 1);
    },
    
    handleInputTagConfirm() {
      let inputValue = this.inputTagValue;
      if (inputValue && this.newTopicForm.tags.indexOf(inputValue) === -1) {
        this.newTopicForm.tags.push(inputValue);
      }
      this.inputTagVisible = false;
      this.inputTagValue = '';
    },
    
    submitNewTopic() {
      // 实际项目中，这里会调用API发布新讨论
      if (!this.newTopicForm.title || !this.newTopicForm.category || !this.newTopicForm.content) {
        this.$message({
          message: '请填写必填字段',
          type: 'warning'
        });
        return;
      }
      
      this.$message({
        message: '讨论主题发布成功！',
        type: 'success'
      });
      this.newTopicDialogVisible = false;
      
      // 模拟发布后刷新列表
      this.fetchTopics();
    }
  }
}
</script>

<style scoped lang="scss">
.forum-container {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    h1 {
      font-size: 28px;
      font-weight: 600;
      margin: 0 0 10px 0;
    }
    
    p {
      font-size: 16px;
      color: #909399;
      margin: 0;
    }
  }
  
  .main-content {
    margin-bottom: 20px;
    
    .forum-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      .category-filter {
        margin-bottom: 15px;
      }
      
      .sort-control {
        .el-dropdown-link {
          cursor: pointer;
          color: #409EFF;
        }
      }
    }
    
    .topic-list {
      .topic-item {
        display: flex;
        padding: 20px 0;
        border-bottom: 1px solid #EBEEF5;
        cursor: pointer;
        
        &:hover {
          background-color: #F5F7FA;
        }
        
        &:last-child {
          border-bottom: none;
        }
        
        .topic-avatar {
          margin-right: 15px;
        }
        
        .topic-content {
          flex: 1;
          overflow: hidden;
          
          .topic-title {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            
            .title-text {
              font-size: 18px;
              font-weight: 600;
              color: #303133;
              margin-right: 10px;
              flex: 1;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            
            .el-tag {
              margin-left: 5px;
            }
          }
          
          .topic-meta {
            font-size: 13px;
            color: #909399;
            margin-bottom: 10px;
            
            .author {
              margin-right: 15px;
            }
          }
          
          .topic-summary {
            font-size: 14px;
            color: #606266;
            margin-bottom: 10px;
            line-height: 1.5;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
          
          .topic-stats {
            display: flex;
            font-size: 13px;
            color: #909399;
            
            .stat-item {
              margin-right: 20px;
              display: flex;
              align-items: center;
              
              i {
                margin-right: 5px;
              }
            }
          }
        }
        
        .topic-last-reply {
          width: 120px;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          justify-content: center;
          
          .last-reply-info {
            text-align: right;
            margin-bottom: 5px;
            
            .last-reply-text {
              font-size: 12px;
              color: #909399;
              display: block;
              margin-bottom: 3px;
            }
            
            .last-reply-time {
              font-size: 12px;
              color: #909399;
            }
          }
        }
      }
    }
    
    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
  
  .sidebar-card {
    margin-bottom: 20px;
    
    .sidebar-actions {
      .new-topic-btn {
        width: 100%;
        margin-bottom: 15px;
      }
    }
    
    .card-header {
      margin-bottom: 15px;
      
      h3 {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
        color: #303133;
      }
    }
    
    .trending-topics {
      .trending-topic-item {
        padding: 10px 0;
        border-bottom: 1px solid #EBEEF5;
        cursor: pointer;
        
        &:last-child {
          border-bottom: none;
        }
        
        &:hover {
          .trending-title {
            color: #409EFF;
          }
        }
        
        .trending-title {
          font-size: 15px;
          margin-bottom: 8px;
          color: #303133;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        
        .trending-meta {
          font-size: 12px;
          color: #909399;
          
          .trending-stat {
            margin-right: 15px;
            
            i {
              margin-right: 3px;
            }
          }
        }
      }
    }
    
    .active-users {
      .active-user-item {
        display: flex;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #EBEEF5;
        
        &:last-child {
          border-bottom: none;
        }
        
        .active-user-info {
          flex: 1;
          margin-left: 10px;
          
          .active-user-name {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 3px;
          }
          
          .active-user-topics {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
    
    .forum-stats {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
      
      .forum-stat-item {
        text-align: center;
        
        .stat-title {
          font-size: 14px;
          color: #909399;
          margin-bottom: 5px;
        }
        
        .stat-value {
          font-size: 20px;
          font-weight: 600;
          color: #303133;
        }
      }
    }
  }
  
  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
  
  .button-new-tag {
    margin-left: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
  }
}

@media (max-width: 768px) {
  .forum-container {
    .forum-header {
      flex-direction: column;
      align-items: flex-start !important;
      
      .category-filter, .sort-control {
        width: 100%;
        margin-bottom: 10px;
      }
    }
    
    .topic-item {
      flex-direction: column;
      
      .topic-avatar {
        margin-bottom: 10px;
      }
      
      .topic-last-reply {
        width: 100% !important;
        flex-direction: row !important;
        justify-content: flex-start !important;
        align-items: center !important;
        margin-top: 10px;
        
        .last-reply-info {
          margin-right: 10px;
          text-align: left !important;
        }
      }
    }
  }
}
</style>