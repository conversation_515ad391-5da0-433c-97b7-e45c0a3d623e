// 颜色变量
$primary-color: #409eff;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$info-color: #909399;

// 文本颜色
$text-color-primary: #303133;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$text-color-placeholder: #c0c4cc;

// 边框颜色
$border-color-base: #dcdfe6;
$border-color-light: #e4e7ed;
$border-color-lighter: #ebeef5;
$border-color-extra-light: #f2f6fc;

// 背景颜色
$bg-color: #ffffff;
$bg-color-page: #f2f3f5;
$bg-color-light: #fcfcfc;

// 字体大小
$font-size-extra-large: 20px;
$font-size-large: 18px;
$font-size-medium: 16px;
$font-size-base: 14px;
$font-size-small: 13px;
$font-size-extra-small: 12px;

// 字体粗细
$font-weight-primary: 500;
$font-weight-secondary: 400;

// 行高
$line-height-primary: 24px;
$line-height-base: 1.5;

// 边框圆角
$border-radius-base: 4px;
$border-radius-small: 2px;
$border-radius-round: 20px;
$border-radius-circle: 100%;

// 盒子阴影
$box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
$box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

// 间距
$spacing-base: 16px;
$spacing-large: 24px;
$spacing-medium: 16px;
$spacing-small: 12px;
$spacing-mini: 8px;

// 断点
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 992px;
$breakpoint-lg: 1200px;
$breakpoint-xl: 1920px;

// Z-index
$z-index-normal: 1;
$z-index-top: 1000;
$z-index-popper: 2000;

// 过渡动画
$transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
$transition-fade: opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
$transition-md-fade: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);

// 组件特定变量
$header-height: 60px;
$sidebar-width: 240px;
$sidebar-collapsed-width: 64px;
$footer-height: 60px;

// 自定义颜色
$brand-primary: #409eff;
$brand-secondary: #909399;
$brand-accent: #e6a23c;

// 深色模式变量
$dark-bg-color: #1a1a1a;
$dark-bg-color-light: #2d2d2d;
$dark-text-color-primary: #e5eaf3;
$dark-text-color-regular: #cfd3dc;
$dark-border-color-base: #4c4d4f;
