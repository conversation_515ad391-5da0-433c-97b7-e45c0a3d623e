const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const { createServer } = require('http');
const { Server } = require('socket.io');
require('dotenv').config();

const config = require('./config');
const logger = require('./utils/logger');

// 尝试导入数据库模块，如果失败则使用内存数据库模式
let database;
try {
  database = require('./database/connection');
} catch (err) {
  logger.warn('数据库模块加载失败，将使用内存数据库模式');
  database = require('./database/memory-db');
}

// 尝试导入Redis模块，如果失败则使用内存缓存
let redis;
try {
  redis = require('./utils/redis');
} catch (err) {
  logger.warn('Redis模块加载失败，将使用内存缓存');
  redis = require('./utils/memory-cache');
}

const app = express();
const server = createServer(app);

// Socket.IO配置
const io = new Server(server, {
  cors: {
    origin: config.cors.origin,
    credentials: config.cors.credentials
  }
});

// 全局中间件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
}));

app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: config.cors.credentials,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 日志中间件
if (config.app.env !== 'test') {
  app.use(morgan('combined', {
    stream: {
      write: (message) => logger.info(message.trim())
    }
  }));
}

// 静态文件服务
app.use('/uploads', express.static('uploads'));

// 健康检查
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: config.app.env,
    version: process.env.npm_package_version || '1.0.0'
  });
});

// 简单的API测试路由
app.get('/api/test', (req, res) => {
  res.json({ message: 'API is working', timestamp: new Date().toISOString() });
});

// Socket.IO事件处理
io.on('connection', (socket) => {
  logger.info(`用户连接: ${socket.id}`);

  // 用户加入房间
  socket.on('join', (data) => {
    if (data.userId) {
      socket.join(`user_${data.userId}`);
      logger.info(`用户 ${data.userId} 加入房间`);
    }
  });

  // 用户离开
  socket.on('disconnect', () => {
    logger.info(`用户断开连接: ${socket.id}`);
  });
});

// 将io实例添加到app中，供其他模块使用
app.set('io', io);

// 启动服务器
const PORT = config.app.port || 8000;

async function startServer() {
  try {
    logger.info('开始启动测试服务器...');
    
    // 尝试连接数据库（可选）
    try {
      logger.info('尝试连接数据库...');
      await database.testConnection();
      logger.info('数据库连接成功');
    } catch (dbError) {
      logger.warn('数据库连接失败，将使用内存数据库模式:', dbError.message);
    }

    // 尝试连接Redis（可选）
    try {
      logger.info('尝试连接Redis...');
      await redis.ping();
      logger.info('Redis连接成功');
    } catch (redisError) {
      logger.warn('Redis连接失败，将使用内存缓存模式:', redisError.message);
    }

    // 启动服务器
    logger.info(`准备在端口 ${PORT} 启动服务器...`);
    server.listen(PORT, () => {
      logger.info(`测试服务器启动成功，端口: ${PORT}`);
      logger.info(`环境: ${config.app.env}`);
      logger.info(`健康检查: http://localhost:${PORT}/health`);
      logger.info(`API测试: http://localhost:${PORT}/api/test`);
    });
    
    server.on('error', (error) => {
      logger.error('服务器错误:', error);
      if (error.code === 'EADDRINUSE') {
        logger.error(`端口 ${PORT} 已被占用`);
      }
    });
    
  } catch (error) {
    logger.error('服务器启动失败:', error);
    process.exit(1);
  }
}

// 直接启动服务器（当直接运行此文件时）
if (require.main === module) {
  startServer().catch(error => {
    logger.error('启动服务器时发生错误:', error);
    process.exit(1);
  });
}

module.exports = { app, startServer };
