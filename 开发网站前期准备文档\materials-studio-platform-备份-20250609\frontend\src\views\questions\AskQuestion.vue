<template>
  <div class="ask-question-page">
    <div class="ask-question-header">
      <h1>提出您的问题</h1>
      <p class="subtitle">详细描述您的问题，获得专业解答</p>
    </div>
    
    <div class="ask-question-content">
      <div class="ask-question-main">
        <el-form 
          :model="questionForm" 
          :rules="rules" 
          ref="questionFormRef" 
          label-position="top"
          status-icon
        >
          <el-form-item label="问题标题" prop="title">
            <el-input 
              v-model="questionForm.title" 
              placeholder="简明扼要地描述您的问题，例如：'如何在 Materials Studio 中设置分子动力学模拟的温度？'"
            />
            <div class="form-item-tip">
              标题应该简洁明了，能够清晰表达问题的核心
            </div>
          </el-form-item>
          
          <el-form-item label="问题详情" prop="content">
            <el-input 
              v-model="questionForm.content" 
              type="textarea" 
              :rows="10"
              placeholder="详细描述您的问题，包括：
1. 您的操作环境（系统、软件版本等）
2. 您尝试解决的步骤
3. 遇到的具体错误或困难
4. 其他可能有帮助的信息"
            />
          </el-form-item>
          
          <el-form-item label="标签" prop="tags">
            <el-select
              v-model="questionForm.tags"
              multiple
              filterable
              allow-create
              default-first-option
              placeholder="选择或创建标签"
              class="tag-select"
            >
              <el-option
                v-for="tag in commonTags"
                :key="tag"
                :label="tag"
                :value="tag"
              />
            </el-select>
            <div class="form-item-tip">
              添加相关标签可以帮助您的问题更容易被找到，最多选择5个标签
            </div>
          </el-form-item>
          
          <el-form-item label="附件">
            <el-upload
              class="attachment-uploader"
              action="#"
              :http-request="uploadAttachment"
              :before-upload="beforeAttachmentUpload"
              :on-remove="handleRemoveAttachment"
              multiple
              :limit="3"
            >
              <el-button type="primary">
                <el-icon><Upload /></el-icon> 上传附件
              </el-button>
              <template #tip>
                <div class="el-upload__tip">
                  可上传图片、文本或压缩文件，每个文件不超过5MB，最多3个附件
                </div>
              </template>
            </el-upload>
          </el-form-item>
          
          <el-form-item v-if="!isAuthenticated">
            <el-alert
              title="请先登录再提问"
              type="warning"
              show-icon
              :closable="false"
            />
          </el-form-item>
          
          <el-form-item>
            <div class="form-actions">
              <el-button @click="$router.push('/questions')">取消</el-button>
              <el-button 
                type="primary" 
                @click="submitQuestion" 
                :loading="submitting"
                :disabled="!isAuthenticated || submitting"
              >
                发布问题
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
      
      <div class="ask-question-sidebar">
        <div class="tips-card">
          <h3>如何提出好问题</h3>
          <ul>
            <li><strong>标题要具体</strong> - 避免过于宽泛的问题</li>
            <li><strong>提供细节</strong> - 包括您的环境、已尝试的方法</li>
            <li><strong>格式化代码</strong> - 如果包含代码，请使用代码格式</li>
            <li><strong>添加相关标签</strong> - 帮助合适的专家找到您的问题</li>
            <li><strong>检查类似问题</strong> - 可能已有解决方案</li>
          </ul>
        </div>
        
        <div class="similar-questions-card" v-if="similarQuestions.length > 0">
          <h3>可能相关的问题</h3>
          <ul>
            <li v-for="q in similarQuestions" :key="q.id">
              <router-link :to="`/questions/${q.id}`">{{ q.title }}</router-link>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { Upload } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import api from '@/api'

const router = useRouter()
const authStore = useAuthStore()
const isAuthenticated = computed(() => authStore.isAuthenticated)

const questionFormRef = ref(null)
const submitting = ref(false)

// 表单数据
const questionForm = reactive({
  title: '',
  content: '',
  tags: [],
  attachments: []
})

// 验证规则
const rules = {
  title: [
    { required: true, message: '请输入问题标题', trigger: 'blur' },
    { min: 10, max: 100, message: '标题长度应在10-100个字符之间', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入问题详情', trigger: 'blur' },
    { min: 30, message: '详情不能少于30个字符', trigger: 'blur' }
  ],
  tags: [
    { required: true, message: '请至少选择一个标签', trigger: 'change' },
    { 
      validator: (rule, value, callback) => {
        if (value.length > 5) {
          callback(new Error('最多选择5个标签'))
        } else {
          callback()
        }
      }, 
      trigger: 'change' 
    }
  ]
}

// 常用标签
const commonTags = [
  'Materials Studio',
  'CASTEP',
  'DMol3',
  'Forcite',
  '分子动力学',
  '密度泛函理论',
  '过渡态搜索',
  '结构优化',
  '吸附',
  '催化',
  '晶体结构',
  '力场',
  '量子化学',
  '电子结构',
  '能带结构'
]

// 相似问题
const similarQuestions = ref([])

// 监听标题变化，搜索相似问题
watch(() => questionForm.title, debounce(async (val) => {
  if (val && val.length > 10) {
    await searchSimilarQuestions(val)
  } else {
    similarQuestions.value = []
  }
}, 500))

// 搜索相似问题
const searchSimilarQuestions = async (query) => {
  try {
    const response = await api.questions.search({ query, limit: 5 })
    
    if (response.success) {
      similarQuestions.value = response.data || []
    }
  } catch (err) {
    console.error('搜索相似问题失败:', err)
  }
}

// 防抖函数
function debounce(fn, delay) {
  let timer = null
  return function(...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

// 附件上传前验证
const beforeAttachmentUpload = (file) => {
  const isLt5M = file.size / 1024 / 1024 < 5
  
  if (!isLt5M) {
    ElMessage.error('附件大小不能超过5MB!')
    return false
  }
  
  return true
}

// 上传附件
const uploadAttachment = async (options) => {
  try {
    // 实际项目中应该调用API上传文件
    // 这里模拟上传
    const fileObj = {
      uid: Date.now(),
      name: options.file.name,
      url: URL.createObjectURL(options.file),
      file: options.file
    }
    
    questionForm.attachments.push(fileObj)
    
    // 成功回调
    if (options.onSuccess) {
      options.onSuccess()
    }
  } catch (error) {
    console.error('上传附件失败:', error)
    ElMessage.error('上传附件失败')
    
    // 失败回调
    if (options.onError) {
      options.onError()
    }
  }
}

// 移除附件
const handleRemoveAttachment = (file) => {
  const index = questionForm.attachments.findIndex(item => item.uid === file.uid)
  if (index !== -1) {
    questionForm.attachments.splice(index, 1)
  }
}

// 提交问题
const submitQuestion = async () => {
  if (!questionFormRef.value) return
  
  await questionFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    if (!isAuthenticated.value) {
      ElMessage.warning('请先登录再提问')
      router.push('/login')
      return
    }
    
    try {
      submitting.value = true
      
      const formData = {
        title: questionForm.title,
        content: questionForm.content,
        tags: questionForm.tags,
        attachments: questionForm.attachments.map(item => ({
          name: item.name,
          url: item.url
        }))
      }
      
      // 调用API发布问题
      const response = await api.questions.create(formData)
      
      if (response.success) {
        ElMessage.success('问题发布成功')
        router.push(`/questions/${response.data.id}`)
      } else {
        throw new Error(response.message || '发布问题失败')
      }
    } catch (err) {
      console.error('发布问题失败:', err)
      ElMessage.error(err.message || '发布问题失败，请重试')
    } finally {
      submitting.value = false
    }
  })
}
</script>

<style scoped>
.ask-question-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.ask-question-header {
  text-align: center;
  margin-bottom: 30px;
}

.ask-question-header h1 {
  font-size: 32px;
  margin-bottom: 10px;
  color: var(--el-color-primary);
}

.subtitle {
  font-size: 16px;
  color: var(--el-text-color-secondary);
}

.ask-question-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 30px;
}

.ask-question-main {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.form-item-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 5px;
}

.tag-select {
  width: 100%;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 20px;
}

.ask-question-sidebar {
  align-self: start;
}

.tips-card, .similar-questions-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.tips-card h3, .similar-questions-card h3 {
  font-size: 16px;
  margin: 0 0 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.tips-card ul, .similar-questions-card ul {
  padding-left: 20px;
  margin: 0;
}

.tips-card li, .similar-questions-card li {
  margin-bottom: 10px;
  line-height: 1.5;
}

.similar-questions-card a {
  color: var(--el-color-primary);
  text-decoration: none;
}

.similar-questions-card a:hover {
  text-decoration: underline;
}

.attachment-uploader {
  width: 100%;
}

@media (max-width: 768px) {
  .ask-question-content {
    grid-template-columns: 1fr;
  }
  
  .ask-question-header h1 {
    font-size: 24px;
  }
}
</style>
