import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'
import { useAuthStore } from './stores/auth'

// 样式文件
import './styles/index.scss'

// 创建应用实例
const app = createApp(App)

// 创建Pinia实例
const pinia = createPinia()

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用插件
app.use(pinia)
app.use(router)
app.use(ElementPlus, {
  size: 'default',
  zIndex: 3000,
})

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err, info)
  ElMessage.error('应用发生错误，请刷新页面重试')
}

// 全局警告处理
app.config.warnHandler = (msg, vm, trace) => {
  console.warn('全局警告:', msg, trace)
}

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 显示加载进度
  if (typeof window !== 'undefined' && window.NProgress) {
    window.NProgress.start()
  }

  const authStore = useAuthStore()
  
  // 检查是否需要认证
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    // 尝试从本地存储恢复登录状态
    await authStore.initializeAuth()
    
    if (!authStore.isAuthenticated) {
      ElMessage.warning('请先登录')
      next({
        name: 'Login',
        query: { redirect: to.fullPath }
      })
      return
    }
  }

  // 检查会员权限
  if (to.meta.requiresMembership && !authStore.isMember) {
    ElMessage.warning('该功能需要会员权限')
    next({
      name: 'Membership',
      query: { redirect: to.fullPath }
    })
    return
  }

  // 检查管理员权限
  if (to.meta.requiresAdmin && !authStore.isAdmin) {
    ElMessage.error('权限不足')
    next({ name: 'Home' })
    return
  }

  // 已登录用户访问登录页面，重定向到首页
  if (to.name === 'Login' && authStore.isAuthenticated) {
    next({ name: 'Home' })
    return
  }

  next()
})

router.afterEach((to, from) => {
  // 隐藏加载进度
  if (typeof window !== 'undefined' && window.NProgress) {
    window.NProgress.done()
  }

  // 更新页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - Materials Studio Platform`
  } else {
    document.title = 'Materials Studio Platform - 专业的分子模拟问答平台'
  }

  // 滚动到顶部
  if (to.hash) {
    setTimeout(() => {
      const element = document.querySelector(to.hash)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    }, 100)
  } else {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }
})

// 挂载应用
app.mount('#app')

// 开发环境下的调试工具
if (import.meta.env.DEV) {
  window.__app__ = app
  window.__router__ = router
  window.__pinia__ = pinia
}

// 生产环境下禁用调试工具
if (import.meta.env.PROD) {
  console.log = () => {}
  console.warn = () => {}
  console.error = () => {}
}
