<template>
  <div class="customer-service">
    <div class="service-button" @click="toggleChat">
      <el-icon><ChatDotRound /></el-icon>
    </div>
    
    <div v-if="showChat" class="chat-window">
      <div class="chat-header">
        <span>在线客服</span>
        <el-icon @click="toggleChat"><Close /></el-icon>
      </div>
      <div class="chat-content">
        <p>欢迎使用Materials Studio Platform！</p>
        <p>如有问题，请联系我们：</p>
        <p>邮箱：<EMAIL></p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const showChat = ref(false)

const toggleChat = () => {
  showChat.value = !showChat.value
}
</script>

<style scoped>
.customer-service {
  position: fixed;
  bottom: 100px;
  right: 30px;
  z-index: 1000;
}

.service-button {
  width: 50px;
  height: 50px;
  background: var(--el-color-success);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.service-button:hover {
  background: var(--el-color-success-light-3);
  transform: translateY(-2px);
}

.chat-window {
  position: absolute;
  bottom: 60px;
  right: 0;
  width: 300px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.chat-header {
  background: var(--el-color-success);
  color: white;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-header .el-icon {
  cursor: pointer;
}

.chat-content {
  padding: 16px;
  font-size: 14px;
  line-height: 1.5;
}

.chat-content p {
  margin-bottom: 8px;
}
</style>
