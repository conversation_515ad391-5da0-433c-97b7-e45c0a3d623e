<template>
  <div class="questions-page">
    <div class="questions-header">
      <h1>Materials Studio 问答社区</h1>
      <p class="subtitle">寻找解决方案，分享您的知识</p>
      
      <div class="header-actions">
        <el-input
          v-model="searchQuery"
          placeholder="搜索问题..."
          class="search-input"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-button type="primary" @click="$router.push('/ask')" :disabled="!isAuthenticated">
          <el-icon><Plus /></el-icon> 提问
        </el-button>
      </div>
    </div>
    
    <div class="questions-content">
      <div class="questions-main">
        <!-- 筛选选项 -->
        <div class="filter-bar">
          <div class="sort-options">
            <span class="label">排序：</span>
            <el-radio-group v-model="sortBy" size="small" @change="fetchQuestions">
              <el-radio-button label="newest">最新</el-radio-button>
              <el-radio-button label="hot">热门</el-radio-button>
              <el-radio-button label="unanswered">未回答</el-radio-button>
            </el-radio-group>
          </div>
          
          <div class="view-options">
            <el-switch
              v-model="onlyShowVerified"
              active-text="只看已解决"
              @change="fetchQuestions"
            />
          </div>
        </div>
        
        <!-- 问题列表 -->
        <div class="questions-list">
          <div v-if="loading" class="loading-container">
            <el-skeleton :rows="10" animated />
          </div>
          
          <div v-else-if="questions.length === 0" class="empty-container">
            <el-empty description="暂无相关问题" />
            <el-button type="primary" @click="$router.push('/ask')">立即提问</el-button>
          </div>
          
          <div v-else>
            <div v-for="question in questions" :key="question.id" class="question-item">
              <div class="question-stats">
                <div class="stat-item">
                  <span class="stat-value">{{ question.voteCount || 0 }}</span>
                  <span class="stat-label">投票</span>
                </div>
                <div class="stat-item" :class="{ 'has-answers': question.answerCount > 0 }">
                  <span class="stat-value">{{ question.answerCount || 0 }}</span>
                  <span class="stat-label">回答</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">{{ question.viewCount || 0 }}</span>
                  <span class="stat-label">浏览</span>
                </div>
              </div>
              
              <div class="question-content">
                <h2 class="question-title">
                  <router-link :to="`/questions/${question.id}`">
                    {{ question.title }}
                  </router-link>
                </h2>
                <p class="question-excerpt">{{ question.summary }}</p>
                <div class="question-meta">
                  <div class="question-tags">
                    <el-tag v-for="tag in question.tags" :key="tag" size="small" @click="filterByTag(tag)">
                      {{ tag }}
                    </el-tag>
                  </div>
                  <div class="question-info">
                    <span class="author">
                      {{ question.authorName }}
                    </span>
                    <span class="time">
                      {{ formatTime(question.createdAt) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 分页 -->
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </div>
      </div>
      
      <div class="questions-sidebar">
        <!-- 热门标签 -->
        <div class="sidebar-section">
          <h3 class="sidebar-title">热门标签</h3>
          <div class="tag-cloud">
            <el-tag 
              v-for="tag in popularTags" 
              :key="tag.name" 
              :type="tag.type"
              effect="plain"
              class="tag-item"
              @click="filterByTag(tag.name)"
            >
              {{ tag.name }}
              <span class="tag-count">{{ tag.count }}</span>
            </el-tag>
          </div>
        </div>
        
        <!-- 热门问题 -->
        <div class="sidebar-section">
          <h3 class="sidebar-title">热门问题</h3>
          <ul class="hot-questions">
            <li v-for="q in hotQuestions" :key="q.id">
              <router-link :to="`/questions/${q.id}`">{{ q.title }}</router-link>
              <div class="hot-question-stats">
                <span>{{ q.viewCount || 0 }} 浏览</span>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { Search, Plus } from '@element-plus/icons-vue'
import api from '@/api'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const isAuthenticated = computed(() => authStore.isAuthenticated)

// 页面状态
const loading = ref(false)
const searchQuery = ref('')
const sortBy = ref('newest')
const onlyShowVerified = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const selectedTag = ref('')

// 数据
const questions = ref([])
const hotQuestions = ref([])
const popularTags = ref([
  { name: 'Materials Studio', count: 156, type: '' },
  { name: '分子动力学', count: 87, type: 'success' },
  { name: 'CASTEP', count: 65, type: 'info' },
  { name: 'Forcite', count: 42, type: 'warning' },
  { name: '密度泛函理论', count: 38, type: 'danger' },
  { name: 'DMol3', count: 32, type: '' },
  { name: '过渡态搜索', count: 29, type: 'info' },
  { name: '吸附', count: 24, type: 'success' },
  { name: '反应路径', count: 21, type: 'warning' },
  { name: '晶体构建', count: 18, type: 'danger' }
])

// 获取问题列表
const fetchQuestions = async () => {
  try {
    loading.value = true
    
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      sortBy: sortBy.value,
      verified: onlyShowVerified.value,
      search: searchQuery.value,
      tag: selectedTag.value
    }
    
    // 调用API获取问题列表
    const response = await api.questions.list(params)
    
    if (response.success) {
      questions.value = response.data.data || []
      total.value = response.data.total || 0
    } else {
      throw new Error(response.message || '获取问题列表失败')
    }
  } catch (err) {
    console.error('获取问题列表失败:', err)
    ElMessage.error('获取问题列表失败，请刷新重试')
  } finally {
    loading.value = false
  }
}

// 获取热门问题
const fetchHotQuestions = async () => {
  try {
    const response = await api.questions.hot()
    
    if (response.success) {
      hotQuestions.value = response.data || []
    }
  } catch (err) {
    console.error('获取热门问题失败:', err)
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchQuestions()
}

// 按标签筛选
const filterByTag = (tag) => {
  selectedTag.value = tag
  currentPage.value = 1
  fetchQuestions()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchQuestions()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchQuestions()
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return ''
  
  const date = new Date(time)
  const now = new Date()
  const diff = Math.floor((now - date) / 1000)
  
  if (diff < 60) {
    return '刚刚'
  } else if (diff < 3600) {
    return `${Math.floor(diff / 60)}分钟前`
  } else if (diff < 86400) {
    return `${Math.floor(diff / 3600)}小时前`
  } else if (diff < 2592000) {
    return `${Math.floor(diff / 86400)}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

// 从URL中获取查询参数
const initFromQuery = () => {
  if (route.query.search) {
    searchQuery.value = route.query.search
  }
  
  if (route.query.tag) {
    selectedTag.value = route.query.tag
  }
  
  if (route.query.sort) {
    sortBy.value = route.query.sort
  }
}

onMounted(() => {
  initFromQuery()
  fetchQuestions()
  fetchHotQuestions()
})
</script>

<style scoped>
.questions-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.questions-header {
  margin-bottom: 30px;
  text-align: center;
}

.questions-header h1 {
  font-size: 32px;
  margin-bottom: 10px;
  color: var(--el-color-primary);
}

.subtitle {
  font-size: 16px;
  color: var(--el-text-color-secondary);
  margin-bottom: 24px;
}

.header-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  max-width: 600px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
}

.questions-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 30px;
}

.questions-main {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--el-border-color-light);
  background: #f9f9f9;
}

.sort-options {
  display: flex;
  align-items: center;
}

.sort-options .label {
  margin-right: 10px;
  color: var(--el-text-color-secondary);
}

.questions-list {
  padding: 0;
}

.loading-container {
  padding: 30px;
}

.empty-container {
  padding: 50px 20px;
  text-align: center;
}

.empty-container .el-button {
  margin-top: 20px;
}

.question-item {
  display: flex;
  padding: 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.question-stats {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 70px;
  margin-right: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10px;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
}

.stat-label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.has-answers .stat-value {
  color: var(--el-color-success);
}

.question-content {
  flex: 1;
}

.question-title {
  font-size: 18px;
  margin: 0 0 10px;
}

.question-title a {
  color: var(--el-text-color-primary);
  text-decoration: none;
}

.question-title a:hover {
  color: var(--el-color-primary);
}

.question-excerpt {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.question-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.question-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.question-tags .el-tag {
  cursor: pointer;
}

.question-info {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.pagination-container {
  padding: 20px;
  display: flex;
  justify-content: center;
}

/* 侧边栏 */
.questions-sidebar {
  align-self: start;
}

.sidebar-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.sidebar-title {
  font-size: 16px;
  margin: 0 0 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tag-item {
  cursor: pointer;
  display: flex;
  align-items: center;
}

.tag-count {
  margin-left: 5px;
  font-size: 12px;
  opacity: 0.8;
}

.hot-questions {
  list-style: none;
  padding: 0;
  margin: 0;
}

.hot-questions li {
  padding: 10px 0;
  border-bottom: 1px solid var(--el-border-color-light);
}

.hot-questions li:last-child {
  border-bottom: none;
}

.hot-questions a {
  display: block;
  color: var(--el-text-color-primary);
  text-decoration: none;
  margin-bottom: 5px;
  font-weight: 500;
}

.hot-questions a:hover {
  color: var(--el-color-primary);
}

.hot-question-stats {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

@media (max-width: 768px) {
  .questions-content {
    grid-template-columns: 1fr;
  }
  
  .questions-header h1 {
    font-size: 24px;
  }
  
  .header-actions {
    flex-direction: column;
  }
  
  .question-item {
    flex-direction: column;
  }
  
  .question-stats {
    flex-direction: row;
    margin-right: 0;
    margin-bottom: 15px;
    justify-content: space-around;
  }
  
  .stat-item {
    margin-bottom: 0;
    margin-right: 15px;
  }
}
</style>
