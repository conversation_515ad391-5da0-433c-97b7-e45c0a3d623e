const jwt = require('jsonwebtoken');
const config = require('../config');

// 尝试导入数据库模块，如果失败则使用内存数据库模式
let database;
try {
  database = require('../database/connection');
} catch (err) {
  database = require('../database/memory-db');
}

// 尝试导入Redis模块，如果失败则使用内存缓存
let redis;
try {
  redis = require('../utils/redis');
} catch (err) {
  redis = require('../utils/memory-cache');
}

const logger = require('../utils/logger');
const { AuthenticationError, AuthorizationError } = require('./errorHandler');

// 验证JWT令牌
const verifyToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AuthenticationError('缺少访问令牌');
    }

    const token = authHeader.substring(7);
    
    // 验证令牌
    const decoded = jwt.verify(token, config.jwt.secret);
    
    // 检查令牌是否在黑名单中
    const isBlacklisted = await redis.exists(`blacklist:${token}`);
    if (isBlacklisted) {
      throw new AuthenticationError('访问令牌已失效');
    }

    // 获取用户信息
    const user = await database.queryOne(
      `SELECT u.*, up.real_name, up.avatar, up.points, up.contributions,
              m.level as member_level, m.end_date as member_end_date
       FROM users u
       LEFT JOIN user_profiles up ON u.id = up.user_id
       LEFT JOIN memberships m ON u.id = m.user_id AND m.status = 1 AND m.end_date >= CURDATE()
       WHERE u.id = ? AND u.status = 1`,
      [decoded.userId]
    );

    if (!user) {
      throw new AuthenticationError('用户不存在或已被禁用');
    }

    // 更新最后活跃时间
    await database.update('users', 
      { last_login: new Date() }, 
      'id = ?', 
      [user.id]
    );

    // 将用户信息添加到请求对象
    req.user = {
      id: user.id,
      username: user.username,
      email: user.email,
      phone: user.phone,
      realName: user.real_name,
      avatar: user.avatar,
      points: user.points || 0,
      contributions: user.contributions || 0,
      memberLevel: user.member_level || 0,
      memberEndDate: user.member_end_date,
      isMember: user.member_level > 0 && user.member_end_date >= new Date(),
      createdAt: user.created_at
    };

    logger.logAuth('token_verified', user.id, {
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError' || 
        error.name === 'TokenExpiredError' || 
        error.name === 'NotBeforeError') {
      next(new AuthenticationError('无效的访问令牌'));
    } else {
      next(error);
    }
  }
};

// 可选的令牌验证（不强制要求登录）
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next();
    }

    const token = authHeader.substring(7);
    
    try {
      const decoded = jwt.verify(token, config.jwt.secret);
      
      // 检查令牌是否在黑名单中
      const isBlacklisted = await redis.exists(`blacklist:${token}`);
      if (isBlacklisted) {
        return next();
      }

      // 获取用户信息
      const user = await database.queryOne(
        `SELECT u.*, up.real_name, up.avatar, up.points, up.contributions,
                m.level as member_level, m.end_date as member_end_date
         FROM users u
         LEFT JOIN user_profiles up ON u.id = up.user_id
         LEFT JOIN memberships m ON u.id = m.user_id AND m.status = 1 AND m.end_date >= CURDATE()
         WHERE u.id = ? AND u.status = 1`,
        [decoded.userId]
      );

      if (user) {
        req.user = {
          id: user.id,
          username: user.username,
          email: user.email,
          phone: user.phone,
          realName: user.real_name,
          avatar: user.avatar,
          points: user.points || 0,
          contributions: user.contributions || 0,
          memberLevel: user.member_level || 0,
          memberEndDate: user.member_end_date,
          isMember: user.member_level > 0 && user.member_end_date >= new Date(),
          createdAt: user.created_at
        };
      }
    } catch (tokenError) {
      // 令牌无效，但不抛出错误，继续处理请求
      logger.warn('可选认证中令牌验证失败:', tokenError.message);
    }

    next();
  } catch (error) {
    next(error);
  }
};

// 检查会员权限
const requireMembership = (minLevel = 1) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        throw new AuthenticationError('需要登录');
      }

      if (!req.user.isMember || req.user.memberLevel < minLevel) {
        throw new AuthorizationError('需要会员权限');
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

// 检查管理员权限
const requireAdmin = async (req, res, next) => {
  try {
    if (!req.user) {
      throw new AuthenticationError('需要登录');
    }

    // 检查是否为管理员
    const admin = await database.queryOne(
      `SELECT au.*, ar.permissions 
       FROM admin_users au
       JOIN admin_roles ar ON au.role_id = ar.id
       WHERE au.user_id = ? AND au.status = 1 AND ar.status = 1`,
      [req.user.id]
    );

    if (!admin) {
      throw new AuthorizationError('需要管理员权限');
    }

    // 解析权限
    let permissions = [];
    try {
      permissions = JSON.parse(admin.permissions || '[]');
    } catch (e) {
      permissions = [];
    }

    req.admin = {
      id: admin.id,
      username: admin.username,
      realName: admin.real_name,
      roleId: admin.role_id,
      permissions
    };

    logger.logAuth('admin_access', req.user.id, {
      adminId: admin.id,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    next();
  } catch (error) {
    next(error);
  }
};

// 检查特定权限
const requirePermission = (permission) => {
  return (req, res, next) => {
    try {
      if (!req.admin) {
        throw new AuthorizationError('需要管理员权限');
      }

      if (!req.admin.permissions.includes(permission) && 
          !req.admin.permissions.includes('*')) {
        throw new AuthorizationError(`需要 ${permission} 权限`);
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

// 检查资源所有权
const requireOwnership = (getResourceOwnerId) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        throw new AuthenticationError('需要登录');
      }

      const ownerId = await getResourceOwnerId(req);
      
      if (ownerId !== req.user.id) {
        throw new AuthorizationError('只能操作自己的资源');
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

// 速率限制检查
const checkRateLimit = (key, limit, window) => {
  return async (req, res, next) => {
    try {
      const identifier = req.user ? `user:${req.user.id}` : `ip:${req.ip}`;
      const rateLimitKey = `rate_limit:${key}:${identifier}`;
      
      const current = await redis.incr(rateLimitKey, window);
      
      if (current > limit) {
        throw new RateLimitError('操作过于频繁，请稍后再试');
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

module.exports = {
  verifyToken,
  optionalAuth,
  requireMembership,
  requireAdmin,
  requirePermission,
  requireOwnership,
  checkRateLimit
};
