// 导入用户数据
import users from './users';

export default [
  {
    id: 1,
    questionId: 1,
    content: `建立Si晶体模型的步骤如下：
1. 打开Materials Studio，点击File -> New -> 3D Atomistic Document创建一个新文档
2. 点击Build -> Crystals -> Crystal Builder打开晶体构建器
3. 在Elements列表中选择Si（硅）
4. 在Space group下拉菜单中选择Fd-3m（立方晶系，227号空间群）
5. 设置晶格参数a = b = c = 5.43 Å, α = β = γ = 90°
6. 点击Build按钮生成晶体结构
7. 点击File -> Save保存模型

如有需要，你还可以使用Supercell工具扩展晶胞大小。`,
    createdAt: '2023-05-01T11:20:00.000Z',
    updatedAt: '2023-05-01T11:20:00.000Z',
    userId: 1,
    user: users.find(u => u.id === 1),
    isAccepted: true,
    voteCount: 12
  },
  {
    id: 2,
    questionId: 2,
    content: `关于CASTEP计算参数设置：

1. 赝势选择：
   - Ultrasoft赝势计算速度较快，适合大多数常规计算
   - Norm-conserving赝势精度较高，适合需要高精度电子结构的计算
   - On-the-fly生成的赝势灵活性高，可以自动为不同元素生成合适的赝势

2. 截断能（Cutoff Energy）：
   - 截断能决定了平面波基组的大小，影响计算精度和效率
   - 一般建议先进行收敛测试，即尝试不同的截断能值直到结果稳定
   - 轻元素（如H, C, N, O）一般200-300 eV足够
   - 过渡金属一般需要350-450 eV
   - 某些特殊元素（如O带f轨道）可能需要500-600 eV

3. 对计算结果的影响：
   - 截断能过低会导致结果不准确，特别是能量、力和应力
   - 赝势选择不当可能会影响电子结构计算
   - 高精度计算（如声子谱、光学性质）对这些参数更敏感

建议对新系统先做参数收敛测试，找到合适的计算参数。`,
    createdAt: '2023-05-10T15:05:00.000Z',
    updatedAt: '2023-05-10T15:05:00.000Z',
    userId: 1,
    user: users.find(u => u.id === 1),
    isAccepted: true,
    voteCount: 8
  },
  {
    id: 3,
    questionId: 3,
    content: `在Forcite模块中，NVT系综（恒体积恒温系综）的几种温度控制方法比较：

1. Nosé温度控制：
   - 理论基础最扎实，能正确生成正则系综
   - 温度波动较大，弛豫时间长
   - 适合平衡态模拟和热力学性质计算
   - 不适合快速升降温过程

2. Andersen温度控制：
   - 通过随机碰撞实现温度控制
   - 容易打断体系的动力学行为
   - 适合达到目标温度分布
   - 不适合计算扩散系数等动力学性质

3. Berendsen温度控制：
   - 温度控制最迅速、稳定
   - 不能严格生成正则系综
   - 适合体系的预平衡和快速加热/冷却
   - 适合非平衡态动力学模拟

实际应用建议：
- 系统预平衡用Berendsen
- 获取平衡态热力学性质用Nosé
- 研究扩散等动力学性质应避免使用Andersen

选择时应考虑研究目标、体系特点和计算效率。`,
    createdAt: '2023-05-15T10:30:00.000Z',
    updatedAt: '2023-05-15T10:30:00.000Z',
    userId: 1,
    user: users.find(u => u.id === 1),
    isAccepted: true,
    voteCount: 15
  }
]; 