<template>
  <div class="database-detail-container">
    <div class="header-section">
      <div class="back-button" @click="goBack">
        <i class="el-icon-arrow-left"></i> 返回数据库列表
      </div>
      <h1 class="detail-title">{{ database.name }}</h1>
      <div class="database-category">{{ database.category }}</div>
    </div>
    
    <el-row :gutter="20">
      <el-col :md="16" :sm="24">
        <el-card class="database-card" shadow="hover">
          <div class="database-header">
            <img v-if="database.logo" :src="database.logo" class="database-logo" alt="数据库标志">
            <div class="database-info">
              <h2>{{ database.name }}</h2>
              <div class="database-provider">提供方: {{ database.provider }}</div>
            </div>
          </div>
          
          <div class="database-description">
            <h3>数据库介绍</h3>
            <p>{{ database.description }}</p>
          </div>
          
          <div class="data-structure">
            <h3>数据结构</h3>
            <el-table :data="database.dataStructure" style="width: 100%">
              <el-table-column prop="field" label="字段名称" width="180"></el-table-column>
              <el-table-column prop="type" label="数据类型" width="180"></el-table-column>
              <el-table-column prop="description" label="说明"></el-table-column>
            </el-table>
          </div>
          
          <div class="data-examples" v-if="database.examples && database.examples.length">
            <h3>数据示例</h3>
            <el-collapse>
              <el-collapse-item v-for="(example, index) in database.examples" :key="index" :title="`示例 ${index + 1}`">
                <pre class="code-block">{{ JSON.stringify(example, null, 2) }}</pre>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-card>
        
        <el-card class="visualization-card" shadow="hover" v-if="database.visualization">
          <h3>数据可视化</h3>
          <div class="visualization-container">
            <!-- 这里可以根据不同的数据库类型，加载不同的可视化组件，如图表、结构展示等 -->
            <div v-if="database.visualization.type === 'chart'" class="chart-container" ref="chartContainer"></div>
            <div v-else-if="database.visualization.type === 'structure'" class="structure-container">
              <!-- 3D结构可视化组件将在这里加载 -->
              <div class="structure-placeholder">3D结构可视化组件</div>
            </div>
            <div v-else class="no-visualization">当前数据库不支持可视化预览</div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :md="8" :sm="24">
        <el-card class="action-card" shadow="hover">
          <h3>数据库操作</h3>
          <div class="action-buttons">
            <el-button type="primary" icon="el-icon-search" @click="openSearch">搜索数据</el-button>
            <el-button type="success" icon="el-icon-download" @click="downloadData" :disabled="!database.downloadable">
              下载数据集
            </el-button>
            <el-button icon="el-icon-document" @click="viewDocumentation">查看文档</el-button>
            <el-button icon="el-icon-share" @click="shareDatabase">分享数据库</el-button>
          </div>
          
          <div class="database-stats">
            <h3>数据库统计</h3>
            <div class="stats-list">
              <div class="stat-item">
                <span class="stat-label">数据条目:</span>
                <span class="stat-value">{{ formatNumber(database.entryCount) }} 条</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">数据大小:</span>
                <span class="stat-value">{{ formatSize(database.dataSize) }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">最后更新:</span>
                <span class="stat-value">{{ formatDate(database.lastUpdate) }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">引用次数:</span>
                <span class="stat-value">{{ formatNumber(database.citations) }}</span>
              </div>
            </div>
          </div>
          
          <div class="database-tags">
            <h3>关键词</h3>
            <div class="tag-list">
              <el-tag v-for="tag in database.tags" :key="tag" size="medium" effect="plain" class="tag-item">
                {{ tag }}
              </el-tag>
            </div>
          </div>
          
          <div class="access-info" v-if="database.accessInfo">
            <h3>访问信息</h3>
            <div class="access-type">
              <i :class="[database.accessInfo.isOpen ? 'el-icon-unlock' : 'el-icon-lock']"></i>
              <span>{{ database.accessInfo.isOpen ? '开放访问' : '需要授权' }}</span>
            </div>
            <p>{{ database.accessInfo.description }}</p>
            <el-button type="primary" size="small" v-if="!database.accessInfo.isOpen" @click="requestAccess">
              申请访问权限
            </el-button>
          </div>
        </el-card>
        
        <el-card class="related-card" shadow="hover" v-if="database.related && database.related.length">
          <h3>相关数据库</h3>
          <div class="related-databases">
            <div v-for="item in database.related" :key="item.id" class="related-item" @click="navigateToDatabase(item.id)">
              <div class="related-name">{{ item.name }}</div>
              <div class="related-category">{{ item.category }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 数据库功能选项卡 -->
    <el-card class="features-card" shadow="hover">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="查询示例" name="query">
          <div class="query-examples">
            <h3>查询示例</h3>
            <div v-for="(query, index) in database.queryExamples" :key="index" class="query-example">
              <div class="query-title">{{ query.title }}</div>
              <pre class="code-block">{{ query.code }}</pre>
              <div class="query-description">{{ query.description }}</div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="使用案例" name="cases">
          <div class="use-cases">
            <el-timeline>
              <el-timeline-item
                v-for="(useCase, index) in database.useCases"
                :key="index"
                :timestamp="useCase.date"
                placement="top"
              >
                <el-card shadow="hover">
                  <h4>{{ useCase.title }}</h4>
                  <p>{{ useCase.description }}</p>
                  <div class="case-reference" v-if="useCase.reference">
                    参考文献: <a :href="useCase.referenceLink" target="_blank">{{ useCase.reference }}</a>
                  </div>
                </el-card>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-tab-pane>
        <el-tab-pane label="用户评价" name="reviews">
          <div class="user-reviews">
            <div class="review-stats">
              <div class="average-rating">
                <div class="rating-number">{{ database.averageRating }}</div>
                <el-rate v-model="database.averageRating" disabled show-score></el-rate>
                <div class="total-reviews">{{ database.reviews ? database.reviews.length : 0 }} 条评价</div>
              </div>
              <el-button type="primary" size="small" @click="showReviewDialog">发表评价</el-button>
            </div>
            
            <div class="reviews-list">
              <div v-for="(review, index) in database.reviews" :key="index" class="review-item">
                <div class="review-header">
                  <div class="review-user">
                    <el-avatar size="small" :src="review.userAvatar"></el-avatar>
                    <span class="user-name">{{ review.userName }}</span>
                  </div>
                  <div class="review-date">{{ formatDate(review.date) }}</div>
                </div>
                <div class="review-rating">
                  <el-rate v-model="review.rating" disabled></el-rate>
                </div>
                <div class="review-content">{{ review.content }}</div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    
    <!-- 评价对话框 -->
    <el-dialog title="发表评价" :visible.sync="reviewDialogVisible" width="50%">
      <el-form :model="reviewForm" label-width="80px">
        <el-form-item label="评分">
          <el-rate v-model="reviewForm.rating"></el-rate>
        </el-form-item>
        <el-form-item label="评价内容">
          <el-input type="textarea" :rows="4" v-model="reviewForm.content" placeholder="请输入您对该数据库的评价"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="reviewDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitReview">提交评价</el-button>
      </span>
    </el-dialog>
    
    <!-- 搜索对话框 -->
    <el-dialog title="搜索数据" :visible.sync="searchDialogVisible" width="70%">
      <div class="search-form">
        <h3>高级搜索</h3>
        <el-form :model="searchForm" label-width="100px">
          <el-row :gutter="20">
            <el-col :md="12">
              <el-form-item label="关键词">
                <el-input v-model="searchForm.keyword" placeholder="请输入搜索关键词"></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="12">
              <el-form-item label="数据类型">
                <el-select v-model="searchForm.dataType" placeholder="请选择数据类型" multiple>
                  <el-option v-for="type in dataTypes" :key="type.value" :label="type.label" :value="type.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :md="12">
              <el-form-item label="发布日期">
                <el-date-picker
                  v-model="searchForm.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :md="12">
              <el-form-item label="数据范围">
                <el-slider
                  v-model="searchForm.valueRange"
                  range
                  :min="0"
                  :max="100"
                  :format-tooltip="formatRangeTooltip"
                ></el-slider>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="searchDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="performSearch">搜索</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DatabaseDetail',
  data() {
    return {
      database: {
        id: '',
        name: '晶体结构数据库',
        category: '材料结构',
        description: '这是一个综合性的晶体材料结构数据库，包含超过100,000种材料的晶体结构数据，覆盖金属、半导体、陶瓷、聚合物等多种材料类型。数据来源于实验测定和理论计算，提供晶格参数、空间群、原子位置等结构信息。',
        provider: '材料科学研究院',
        logo: 'https://via.placeholder.com/150',
        entryCount: 105267,
        dataSize: **********, // 1.5GB in bytes
        lastUpdate: '2023-12-15',
        citations: 1283,
        downloadable: true,
        dataStructure: [
          { field: 'material_id', type: 'String', description: '材料唯一标识符' },
          { field: 'formula', type: 'String', description: '化学式' },
          { field: 'space_group', type: 'Integer', description: '空间群编号' },
          { field: 'crystal_system', type: 'String', description: '晶系' },
          { field: 'lattice_parameters', type: 'Object', description: '晶格参数 (a, b, c, alpha, beta, gamma)' }
        ],
        examples: [
          {
            material_id: "mp-149",
            formula: "Si",
            space_group: 227,
            crystal_system: "cubic",
            lattice_parameters: {
              a: 5.468,
              b: 5.468,
              c: 5.468,
              alpha: 90,
              beta: 90,
              gamma: 90
            }
          }
        ],
        tags: ['晶体结构', '材料科学', '晶格参数', 'X射线衍射', '元素周期表'],
        accessInfo: {
          isOpen: true,
          description: '该数据库对学术用户完全开放，商业用户需申请授权。'
        },
        visualization: {
          type: 'structure'
        },
        related: [
          { id: '2', name: '金属材料数据库', category: '材料性能' },
          { id: '3', name: '半导体材料库', category: '电子材料' }
        ],
        queryExamples: [
          {
            title: '基于化学成分的查询',
            code: 'db.find({\n  "elements": {"$all": ["Si", "O"]},\n  "nelements": {"$lte": 3}\n}).sort({"formation_energy_per_atom": 1})',
            description: '查询所有含有硅和氧、元素数量不超过3的材料，按形成能升序排列'
          },
          {
            title: '晶体结构筛选',
            code: 'db.find({\n  "crystal_system": "cubic",\n  "space_group.number": {"$gte": 200}\n})',
            description: '查询所有立方晶系且空间群号大于等于200的材料'
          }
        ],
        useCases: [
          {
            title: '新型太阳能电池材料研究',
            date: '2023-06-15',
            description: '研究人员使用该数据库中的钙钛矿结构数据，通过高通量计算筛选了一系列潜在的太阳能电池材料，其中5种材料表现出优异的光伏性能。',
            reference: 'Zhang et al., Advanced Energy Materials (2023)',
            referenceLink: '#'
          },
          {
            title: '机器学习辅助材料设计',
            date: '2022-11-03',
            description: '基于该数据库中的10万种材料结构，开发了一个机器学习模型，能够预测未知材料的稳定性和电子性质，加速了新型半导体材料的筛选过程。',
            reference: 'Wang et al., Nature Computational Science (2022)',
            referenceLink: '#'
          }
        ],
        averageRating: 4.7,
        reviews: [
          {
            userName: '李研究员',
            userAvatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
            rating: 5,
            date: '2023-10-18',
            content: '数据质量很高，接口也很易用。特别是3D结构可视化功能非常实用，帮助我们团队更直观地理解材料结构。'
          },
          {
            userName: '张教授',
            userAvatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
            rating: 4,
            date: '2023-09-05',
            content: '覆盖范围很广，数据更新也比较及时。希望能增加更多的二维材料数据。'
          }
        ]
      },
      activeTab: 'query',
      reviewDialogVisible: false,
      searchDialogVisible: false,
      reviewForm: {
        rating: 5,
        content: ''
      },
      searchForm: {
        keyword: '',
        dataType: [],
        dateRange: '',
        valueRange: [20, 80]
      },
      dataTypes: [
        { value: 'crystal', label: '晶体结构' },
        { value: 'electronic', label: '电子性质' },
        { value: 'mechanical', label: '力学性质' },
        { value: 'thermal', label: '热学性质' }
      ]
    }
  },
  created() {
    // 从路由参数获取数据库ID
    const databaseId = this.$route.params.type || '1'
    this.loadDatabaseDetails(databaseId)
  },
  mounted() {
    // 如果有可视化组件，在这里初始化
    if (this.database.visualization && this.database.visualization.type === 'chart') {
      this.initVisualization()
    }
  },
  methods: {
    loadDatabaseDetails(id) {
      // 实际项目中，这里会调用API获取数据库详情
      console.log('加载数据库ID:', id)
      // 此处为模拟数据，实际应用中应该通过API获取
    },
    goBack() {
      this.$router.push('/databases')
    },
    formatNumber(num) {
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    },
    formatSize(bytes) {
      if (bytes < 1024) return bytes + ' B'
      else if (bytes < 1048576) return (bytes / 1024).toFixed(2) + ' KB'
      else if (bytes < 1073741824) return (bytes / 1048576).toFixed(2) + ' MB'
      else return (bytes / 1073741824).toFixed(2) + ' GB'
    },
    formatDate(dateStr) {
      return new Date(dateStr).toLocaleDateString('zh-CN', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      })
    },
    formatRangeTooltip(val) {
      return `${val}%`
    },
    initVisualization() {
      // 在这里初始化可视化组件，如 ECharts 或 Three.js
      console.log('初始化数据可视化组件')
    },
    openSearch() {
      this.searchDialogVisible = true
    },
    downloadData() {
      // 实际项目中，这里会调用API下载数据
      this.$message({
        message: '开始下载数据集，请稍候...',
        type: 'success'
      })
    },
    viewDocumentation() {
      // 实际项目中，这里会跳转到文档页面
      this.$message({
        message: '正在加载文档...',
        type: 'info'
      })
    },
    shareDatabase() {
      // 实现分享功能
      this.$message({
        message: '生成分享链接成功！已复制到剪贴板',
        type: 'success'
      })
    },
    requestAccess() {
      // 实际项目中，这里会显示申请表单或跳转到申请页面
      this.$message({
        message: '即将跳转到访问权限申请页面...',
        type: 'info'
      })
    },
    navigateToDatabase(id) {
      // 导航到其他数据库
      this.$router.push(`/databases/${id}`)
    },
    showReviewDialog() {
      this.reviewDialogVisible = true
    },
    submitReview() {
      // 提交评价
      this.$message({
        message: '感谢您的评价！',
        type: 'success'
      })
      this.reviewDialogVisible = false
      // 实际项目中，这里会调用API提交评价
    },
    performSearch() {
      // 执行搜索
      this.$message({
        message: '正在执行搜索...',
        type: 'info'
      })
      this.searchDialogVisible = false
      // 实际项目中，这里会调用API执行搜索
    }
  }
}
</script>

<style scoped lang="scss">
.database-detail-container {
  padding: 20px;
  
  .header-section {
    margin-bottom: 30px;
    
    .back-button {
      display: inline-block;
      cursor: pointer;
      margin-bottom: 15px;
      color: #409EFF;
      font-size: 16px;
      
      &:hover {
        text-decoration: underline;
      }
      
      i {
        margin-right: 5px;
      }
    }
    
    .detail-title {
      font-size: 28px;
      font-weight: 600;
      margin: 0 0 10px 0;
    }
    
    .database-category {
      font-size: 16px;
      color: #909399;
      margin-bottom: 10px;
    }
  }
  
  .database-card {
    margin-bottom: 20px;
    
    .database-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      
      .database-logo {
        width: 80px;
        height: 80px;
        object-fit: contain;
        margin-right: 20px;
      }
      
      .database-info {
        h2 {
          margin: 0 0 10px 0;
          font-size: 22px;
        }
        
        .database-provider {
          color: #606266;
          font-size: 14px;
        }
      }
    }
    
    .database-description {
      margin-bottom: 30px;
      
      h3 {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 15px;
        color: #303133;
      }
      
      p {
        font-size: 14px;
        line-height: 1.8;
        color: #606266;
      }
    }
    
    .data-structure, .data-examples {
      margin-bottom: 30px;
      
      h3 {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 15px;
        color: #303133;
      }
    }
    
    .code-block {
      background-color: #f5f7fa;
      border-radius: 4px;
      padding: 15px;
      font-family: 'Courier New', Courier, monospace;
      overflow-x: auto;
      margin-bottom: 15px;
      font-size: 13px;
    }
  }
  
  .visualization-card {
    margin-bottom: 20px;
    
    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 15px;
      color: #303133;
    }
    
    .visualization-container {
      height: 400px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .chart-container {
        width: 100%;
        height: 100%;
      }
      
      .structure-container {
        width: 100%;
        height: 100%;
        
        .structure-placeholder {
          width: 100%;
          height: 100%;
          background-color: #f5f7fa;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 18px;
          color: #909399;
        }
      }
      
      .no-visualization {
        color: #909399;
        font-size: 16px;
      }
    }
  }
  
  .action-card, .related-card {
    margin-bottom: 20px;
    
    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 15px;
      color: #303133;
    }
    
    .action-buttons {
      margin-bottom: 25px;
      display: flex;
      flex-direction: column;
      gap: 10px;
      
      .el-button {
        width: 100%;
      }
    }
    
    .database-stats {
      margin-bottom: 25px;
      
      .stats-list {
        .stat-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 12px;
          font-size: 14px;
          
          .stat-label {
            color: #909399;
          }
          
          .stat-value {
            font-weight: 500;
            color: #303133;
          }
        }
      }
    }
    
    .database-tags {
      margin-bottom: 25px;
      
      .tag-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        
        .tag-item {
          margin-right: 0;
        }
      }
    }
    
    .access-info {
      .access-type {
        margin-bottom: 10px;
        font-size: 14px;
        display: flex;
        align-items: center;
        
        i {
          margin-right: 8px;
          font-size: 16px;
          color: #409EFF;
        }
      }
      
      p {
        font-size: 14px;
        line-height: 1.6;
        color: #606266;
        margin-bottom: 15px;
      }
    }
  }
  
  .related-card {
    .related-databases {
      .related-item {
        padding: 12px;
        border-bottom: 1px solid #EBEEF5;
        cursor: pointer;
        
        &:last-child {
          border-bottom: none;
        }
        
        &:hover {
          background-color: #F5F7FA;
        }
        
        .related-name {
          font-size: 15px;
          font-weight: 500;
          color: #409EFF;
          margin-bottom: 6px;
        }
        
        .related-category {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
  
  .features-card {
    margin-bottom: 20px;
    
    .query-examples {
      .query-example {
        margin-bottom: 25px;
        
        .query-title {
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 10px;
          color: #303133;
        }
        
        .query-description {
          font-size: 14px;
          color: #606266;
          margin-top: 10px;
        }
      }
    }
    
    .use-cases {
      .case-reference {
        margin-top: 10px;
        font-size: 13px;
        color: #909399;
        
        a {
          color: #409EFF;
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
    
    .user-reviews {
      .review-stats {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
        
        .average-rating {
          display: flex;
          align-items: center;
          
          .rating-number {
            font-size: 32px;
            font-weight: 600;
            color: #303133;
            margin-right: 15px;
          }
          
          .el-rate {
            margin-right: 10px;
          }
          
          .total-reviews {
            font-size: 14px;
            color: #909399;
          }
        }
      }
      
      .reviews-list {
        .review-item {
          padding: 15px 0;
          border-bottom: 1px solid #EBEEF5;
          
          &:last-child {
            border-bottom: none;
          }
          
          .review-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            
            .review-user {
              display: flex;
              align-items: center;
              
              .user-name {
                margin-left: 10px;
                font-size: 14px;
                font-weight: 500;
              }
            }
            
            .review-date {
              font-size: 12px;
              color: #909399;
            }
          }
          
          .review-rating {
            margin-bottom: 10px;
          }
          
          .review-content {
            font-size: 14px;
            line-height: 1.6;
            color: #606266;
          }
        }
      }
    }
  }
  
  // 对话框样式
  .search-form {
    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 20px;
      color: #303133;
    }
  }
}

@media (max-width: 768px) {
  .database-detail-container {
    .header-section {
      .detail-title {
        font-size: 24px;
      }
    }
    
    .visualization-container {
      height: 300px !important;
    }
  }
}
</style>