@echo off
echo Starting Materials Studio Platform...
echo ====================================

echo Checking ports...
netstat -an | findstr :3000
netstat -an | findstr :8001

echo.
echo Installing dependencies if needed...
call npm run install:all

echo.
echo Setting up environment...
cd frontend
echo. > .env.local
echo VITE_API_BASE_URL=http://localhost:8001/api>> .env.local
echo VITE_USE_MOCK_API=false>> .env.local
cd ..

echo.
echo Starting backend service...
cd backend
start "Backend" cmd /k "npm run dev"
cd ..

echo Waiting for backend to start...
timeout /t 10 /nobreak >nul

echo Starting frontend service...
cd frontend
set "BROWSER=none"
start "Frontend" cmd /k "npm run dev"
cd ..

echo Waiting for frontend to start...
timeout /t 8 /nobreak >nul

echo Opening website...
start http://localhost:3000

echo.
echo Services started!
echo Frontend: http://localhost:3000
echo Backend: http://localhost:8001
echo.
echo Real data mode activated (using in-memory database)
echo.
echo Demo accounts:
echo Admin: admin / admin123456
echo.
pause
