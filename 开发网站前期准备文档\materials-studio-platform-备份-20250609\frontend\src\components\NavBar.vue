<template>
  <div class="navbar">
    <el-menu
      :default-active="activeIndex"
      mode="horizontal"
      router
      background-color="#545c64"
      text-color="#fff"
      active-text-color="#ffd04b"
    >
      <div class="logo-container">
        <router-link to="/">
          <img src="../assets/logo.png" alt="Logo" class="logo" />
          <span class="logo-text">Materials Studio</span>
        </router-link>
      </div>
      
      <div class="menu-items">
        <el-menu-item index="/" route="/">首页</el-menu-item>
        
        <!-- 数据资源菜单 -->
        <el-submenu index="data">
          <template #title>数据资源</template>
          <el-menu-item index="/databases" route="/databases">材料数据库</el-menu-item>
          <el-menu-item index="/search" route="/search">高级搜索</el-menu-item>
          <el-menu-item index="/data-analysis" route="/data-analysis">数据分析</el-menu-item>
          <el-menu-item index="/structure-viewer" route="/structure-viewer">结构可视化</el-menu-item>
        </el-submenu>
        
        <!-- 问答社区菜单 -->
        <el-submenu index="community">
          <template #title>问答社区</template>
          <el-menu-item index="/questions" route="/questions">浏览问题</el-menu-item>
          <el-menu-item index="/ask" route="/ask">提问</el-menu-item>
          <el-menu-item index="/forum" route="/forum">讨论区</el-menu-item>
        </el-submenu>
        
        <!-- 学习资源菜单 -->
        <el-submenu index="resources">
          <template #title>学习资源</template>
          <el-menu-item index="/resources" route="/resources">资源中心</el-menu-item>
          <el-menu-item index="/downloads" route="/downloads">资源下载</el-menu-item>
        </el-submenu>
        
        <el-menu-item index="/about" route="/about">关于我们</el-menu-item>
      </div>
      
      <div class="navbar-right">
        <!-- 搜索框 -->
        <div class="search-box">
          <el-input
            v-model="searchQuery"
            placeholder="搜索问题或资源..."
            clearable
            @keyup.enter="search"
          >
            <template #suffix>
              <el-button icon="el-icon-search" circle @click="search"></el-button>
            </template>
          </el-input>
        </div>
        
        <!-- 用户菜单 -->
        <div class="user-menu" v-if="isLoggedIn">
          <el-dropdown trigger="click" @command="handleCommand">
            <div class="user-avatar">
              <el-avatar :size="40" :src="userAvatar"></el-avatar>
              <span class="username">{{ username }}</span>
              <i class="el-icon-arrow-down"></i>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                <el-dropdown-item command="messages">消息通知<el-badge v-if="unreadMessages" :value="unreadMessages" class="message-badge"></el-badge></el-dropdown-item>
                <el-dropdown-item command="favorites">我的收藏</el-dropdown-item>
                <el-dropdown-item command="downloads">下载记录</el-dropdown-item>
                <el-dropdown-item divided command="settings">账号设置</el-dropdown-item>
                <el-dropdown-item v-if="isAdmin" command="admin">管理后台</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        
        <!-- 未登录状态 -->
        <div class="auth-buttons" v-else>
          <el-button type="text" @click="login">登录</el-button>
          <el-button type="primary" @click="register">注册</el-button>
        </div>
      </div>
    </el-menu>
  </div>
</template>

<script>
export default {
  name: 'NavBar',
  data() {
    return {
      searchQuery: '',
      isLoggedIn: false,
      isAdmin: false,
      username: '用户名',
      userAvatar: 'https://cube.elemecdn.com/3/7c/********************************.png',
      unreadMessages: 5
    }
  },
  computed: {
    activeIndex() {
      return this.$route.path
    }
  },
  methods: {
    search() {
      if (this.searchQuery.trim()) {
        this.$router.push({
          path: '/search',
          query: { q: this.searchQuery }
        })
      }
    },
    handleCommand(command) {
      switch (command) {
        case 'profile':
          this.$router.push('/profile')
          break
        case 'messages':
          this.$router.push('/messages')
          break
        case 'favorites':
          this.$router.push('/favorites')
          break
        case 'downloads':
          this.$router.push('/downloads')
          break
        case 'settings':
          this.$router.push('/settings')
          break
        case 'admin':
          this.$router.push('/admin')
          break
        case 'logout':
          this.logout()
          break
      }
    },
    login() {
      this.$router.push('/login')
    },
    register() {
      this.$router.push('/register')
    },
    logout() {
      // 退出登录逻辑
      this.$message.success('退出登录成功')
      this.isLoggedIn = false
      this.$router.push('/')
    }
  },
  created() {
    // 模拟登录状态
    this.isLoggedIn = localStorage.getItem('isLoggedIn') === 'true'
    this.isAdmin = localStorage.getItem('isAdmin') === 'true'
  }
}
</script>

<style scoped>
.navbar {
  position: relative;
}

.logo-container {
  float: left;
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.logo-container a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #fff;
}

.logo {
  height: 40px;
  margin-right: 10px;
}

.logo-text {
  font-size: 18px;
  font-weight: bold;
}

.menu-items {
  display: flex;
  margin-left: 50px;
}

.navbar-right {
  position: absolute;
  right: 20px;
  top: 0;
  height: 60px;
  display: flex;
  align-items: center;
}

.search-box {
  margin-right: 20px;
  width: 220px;
}

.user-menu {
  display: flex;
  align-items: center;
}

.user-avatar {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #fff;
}

.username {
  margin: 0 10px;
}

.auth-buttons .el-button {
  color: #fff;
  margin-left: 10px;
}

.message-badge {
  margin-top: -2px;
  margin-right: -5px;
}
</style> 