// 尝试导入数据库模块，如果失败则使用内存数据库模式
let database;
try {
  database = require('./connection');
} catch (err) {
  database = require('./memory-db');
}

const logger = require('../utils/logger');

/**
 * 封装数据库查询函数
 * @param {string} sql - SQL查询语句
 * @param {Array} params - 查询参数
 * @returns {Promise<Array>} - 查询结果
 */
async function query(sql, params = []) {
  try {
    const result = await database.query(sql, params);
    return result.rows;
  } catch (error) {
    logger.error('数据库查询错误:', {
      sql,
      params,
      error: error.message
    });
    throw error;
  }
}

/**
 * 获取单条记录
 * @param {string} sql - SQL查询语句
 * @param {Array} params - 查询参数
 * @returns {Promise<Object|null>} - 查询结果
 */
async function queryOne(sql, params = []) {
  const results = await query(sql, params);
  return results.length > 0 ? results[0] : null;
}

/**
 * 执行事务操作
 * @param {Function} callback - 事务回调函数
 * @returns {Promise<any>} - 事务执行结果
 */
async function transaction(callback) {
  return database.transaction(callback);
}

/**
 * 插入记录
 * @param {string} table - 表名
 * @param {Object} data - 插入数据
 * @returns {Promise<Object>} - 插入结果
 */
async function insert(table, data) {
  try {
    return await database.insert(table, data);
  } catch (error) {
    logger.error(`插入 ${table} 表数据失败:`, error);
    throw error;
  }
}

/**
 * 更新记录
 * @param {string} table - 表名
 * @param {Object} data - 更新数据
 * @param {string} where - WHERE子句
 * @param {Array} whereParams - WHERE参数
 * @returns {Promise<Object>} - 更新结果
 */
async function update(table, data, where, whereParams = []) {
  try {
    return await database.update(table, data, where, whereParams);
  } catch (error) {
    logger.error(`更新 ${table} 表数据失败:`, error);
    throw error;
  }
}

/**
 * 删除记录
 * @param {string} table - 表名
 * @param {string} where - WHERE子句
 * @param {Array} whereParams - WHERE参数
 * @returns {Promise<Object>} - 删除结果
 */
async function remove(table, where, whereParams = []) {
  try {
    return await database.delete(table, where, whereParams);
  } catch (error) {
    logger.error(`删除 ${table} 表数据失败:`, error);
    throw error;
  }
}

/**
 * 分页查询
 * @param {string} sql - SQL查询语句
 * @param {Array} params - 查询参数
 * @param {number} page - 页码
 * @param {number} limit - 每页记录数
 * @returns {Promise<Object>} - 分页结果
 */
async function paginate(sql, params = [], page = 1, limit = 10) {
  try {
    return await database.paginate(sql, params, page, limit);
  } catch (error) {
    logger.error('分页查询失败:', error);
    throw error;
  }
}

/**
 * 检查数据库连接状态
 * @returns {boolean} - 连接状态
 */
function isHealthy() {
  return database.isHealthy();
}

/**
 * 连接数据库
 * @returns {Promise<void>}
 */
async function connect() {
  try {
    await database.connect();
  } catch (error) {
    logger.error('连接数据库失败:', error);
    throw error;
  }
}

/**
 * 关闭数据库连接
 * @returns {Promise<void>}
 */
async function close() {
  try {
    await database.close();
  } catch (error) {
    logger.error('关闭数据库连接失败:', error);
    throw error;
  }
}

module.exports = {
  query,
  queryOne,
  transaction,
  insert,
  update,
  remove,
  paginate,
  isHealthy,
  connect,
  close
}; 