{"name": "concurrently", "version": "7.6.0", "description": "Run commands concurrently", "main": "index.js", "types": "dist/src/index.d.ts", "type": "commonjs", "bin": {"concurrently": "./dist/bin/concurrently.js", "conc": "./dist/bin/concurrently.js"}, "engines": {"node": "^12.20.0 || ^14.13.0 || >=16.0.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js", "default": "./index.js", "types": "./dist/src/index.d.ts"}, "./package.json": "./package.json"}, "scripts": {"build": "tsc --build", "postbuild": "chmod +x dist/bin/concurrently.js", "clean": "tsc --build --clean", "format": "prettier --check '**/*.{json,y?(a)ml,md}'", "format:fix": "npm run format -- --write", "lint": "eslint --ignore-path .gitignore --ext mjs,js,ts .", "lint:fix": "npm run lint -- --fix", "prepublishOnly": "npm run build", "report-coverage": "cat coverage/lcov.info | coveralls", "test": "jest"}, "repository": {"type": "git", "url": "https://github.com/open-cli-tools/concurrently.git"}, "funding": "https://github.com/open-cli-tools/concurrently?sponsor=1", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"chalk": "^4.1.0", "date-fns": "^2.29.1", "lodash": "^4.17.21", "rxjs": "^7.0.0", "shell-quote": "^1.7.3", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0", "tree-kill": "^1.2.2", "yargs": "^17.3.1"}, "devDependencies": {"@hirez_io/observer-spy": "^2.2.0", "@swc/core": "^1.2.224", "@swc/jest": "^0.2.21", "@types/jest": "^28.1.8", "@types/lodash": "^4.14.178", "@types/node": "^16.11.47", "@types/shell-quote": "^1.7.1", "@types/supports-color": "^8.1.1", "@types/yargs": "^17.0.11", "@typescript-eslint/eslint-plugin": "^5.33.0", "@typescript-eslint/parser": "^5.33.0", "coveralls-next": "^4.1.2", "ctrlc-wrapper": "^0.0.4", "esbuild": "^0.15.1", "eslint": "^8.21.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.0.4", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-simple-import-sort": "^8.0.0", "jest": "^28.1.3", "jest-create-mock-instance": "^2.0.0", "lint-staged": "^12.4.1", "prettier": "^2.6.2", "simple-git-hooks": "^2.7.0", "string-argv": "^0.3.1", "typescript": "~4.9.3"}, "files": ["dist", "index.js", "index.mjs", "!**/fixtures", "!**/*.spec.js", "!**/*.spec.d.ts"], "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "lint-staged": {"*.m?{js,ts}": "eslint --fix", "*.{json,y?(a)ml,md}": "prettier --write"}}