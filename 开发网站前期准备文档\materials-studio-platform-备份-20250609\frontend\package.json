{"name": "materials-studio-platform-frontend", "version": "1.0.0", "description": "Materials Studio答疑平台前端应用", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext vue,js,jsx,cjs,mjs,ts,tsx,cts,mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "test": "vitest", "test:coverage": "vitest --coverage"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "element-plus": "^2.3.8", "@element-plus/icons-vue": "^2.1.0", "axios": "^1.4.0", "dayjs": "^1.11.9", "marked": "^5.1.1", "highlight.js": "^11.8.0", "nprogress": "^0.2.0", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "sortablejs": "^1.15.0", "echarts": "^5.4.3", "vue-echarts": "^6.6.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "vite": "^4.4.5", "sass": "^1.64.1", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "eslint-config-prettier": "^8.8.0", "prettier": "^3.0.0", "vitest": "^0.34.1", "@vue/test-utils": "^2.4.1", "jsdom": "^22.1.0", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "unplugin-icons": "^0.16.5", "@iconify/json": "^2.2.82"}, "keywords": ["vue3", "element-plus", "materials-studio", "qa-platform"], "author": "Materials Studio Platform Team", "license": "MIT"}