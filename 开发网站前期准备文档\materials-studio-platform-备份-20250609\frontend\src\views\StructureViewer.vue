<template>
  <div class="structure-viewer-container">
    <div class="header-section">
      <div class="back-button" @click="goBack">
        <i class="el-icon-arrow-left"></i> 返回
      </div>
      <h1 class="viewer-title">{{ structure.formula || '晶体结构查看器' }}</h1>
      <div class="structure-info">{{ structure.name }}</div>
    </div>

    <el-row :gutter="20">
      <el-col :md="16" :xs="24">
        <el-card class="viewer-card" shadow="hover">
          <div class="viewer-toolbar">
            <el-button-group>
              <el-tooltip content="旋转模式" placement="top">
                <el-button :class="{ active: controlMode === 'rotate' }" @click="setControlMode('rotate')" icon="el-icon-refresh-right" size="small"></el-button>
              </el-tooltip>
              <el-tooltip content="平移模式" placement="top">
                <el-button :class="{ active: controlMode === 'pan' }" @click="setControlMode('pan')" icon="el-icon-rank" size="small"></el-button>
              </el-tooltip>
              <el-tooltip content="缩放模式" placement="top">
                <el-button :class="{ active: controlMode === 'zoom' }" @click="setControlMode('zoom')" icon="el-icon-zoom-in" size="small"></el-button>
              </el-tooltip>
            </el-button-group>

            <div class="view-controls">
              <el-dropdown trigger="click" @command="handleViewChange">
                <el-button size="small">
                  视图模式 <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="ball-stick">球棍模型</el-dropdown-item>
                  <el-dropdown-item command="space-filling">空间填充模型</el-dropdown-item>
                  <el-dropdown-item command="wireframe">线框模型</el-dropdown-item>
                  <el-dropdown-item command="ribbon">丝带模型</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>

              <el-dropdown trigger="click" @command="handleUnitCellDisplay">
                <el-button size="small">
                  晶胞显示 <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="show">显示晶胞</el-dropdown-item>
                  <el-dropdown-item command="hide">隐藏晶胞</el-dropdown-item>
                  <el-dropdown-item command="expand">显示扩展晶胞(2x2x2)</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>

              <el-dropdown trigger="click" @command="handleColorScheme">
                <el-button size="small">
                  配色方案 <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="cpk">CPK配色</el-dropdown-item>
                  <el-dropdown-item command="element">元素周期表配色</el-dropdown-item>
                  <el-dropdown-item command="charge">电荷配色</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>

            <div class="view-actions">
              <el-tooltip content="重置视图" placement="top">
                <el-button @click="resetView" icon="el-icon-refresh" size="small" circle></el-button>
              </el-tooltip>
              <el-tooltip content="截图" placement="top">
                <el-button @click="takeScreenshot" icon="el-icon-camera" size="small" circle></el-button>
              </el-tooltip>
              <el-tooltip content="全屏" placement="top">
                <el-button @click="toggleFullscreen" icon="el-icon-full-screen" size="small" circle></el-button>
              </el-tooltip>
            </div>
          </div>

          <div class="viewer-container" ref="viewerContainer">
            <!-- 3D结构查看器将在这里初始化 -->
            <div v-if="!structureLoaded" class="loading-placeholder">
              <el-spin class="loading-spinner" size="large"></el-spin>
              <span class="loading-text">加载晶体结构中...</span>
            </div>
          </div>

          <div class="viewer-statusbar">
            <div class="atom-info" v-if="selectedAtom">
              已选中: {{ selectedAtom.element }} - {{ selectedAtom.index }}, 位置: ({{ formatCoord(selectedAtom.x) }}, {{ formatCoord(selectedAtom.y) }}, {{ formatCoord(selectedAtom.z) }})
            </div>
            <div class="camera-info">
              视角: {{ cameraPosition.x.toFixed(2) }}, {{ cameraPosition.y.toFixed(2) }}, {{ cameraPosition.z.toFixed(2) }}
            </div>
          </div>
        </el-card>

        <el-card class="measurement-card" shadow="hover">
          <div class="card-title">
            <h3>结构测量</h3>
            <el-switch
              v-model="measurementActive"
              active-text="测量模式"
              inactive-text="浏览模式"
              @change="toggleMeasurementMode"
            ></el-switch>
          </div>

          <div class="measurement-tools" v-if="measurementActive">
            <el-radio-group v-model="measurementType" size="small">
              <el-radio-button label="distance">原子距离</el-radio-button>
              <el-radio-button label="angle">键角</el-radio-button>
              <el-radio-button label="dihedral">二面角</el-radio-button>
            </el-radio-group>

            <div class="measurement-instructions">
              <template v-if="measurementType === 'distance'">
                请选择两个原子来测量距离
              </template>
              <template v-else-if="measurementType === 'angle'">
                请选择三个原子来测量键角
              </template>
              <template v-else>
                请选择四个原子来测量二面角
              </template>
            </div>

            <div class="measurement-results" v-if="measurements.length > 0">
              <h4>测量结果</h4>
              <el-table :data="measurements" size="small" style="width: 100%">
                <el-table-column prop="type" label="类型" width="80">
                  <template slot-scope="scope">
                    <el-tag size="mini" :type="getMeasurementTagType(scope.row.type)">
                      {{ getMeasurementTypeName(scope.row.type) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="atoms" label="原子">
                  <template slot-scope="scope">
                    <span v-for="(atom, index) in scope.row.atoms" :key="index">
                      {{ atom.element }}{{ atom.index }}
                      <span v-if="index < scope.row.atoms.length - 1"> - </span>
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="value" label="数值" width="120">
                  <template slot-scope="scope">
                    {{ scope.row.value.toFixed(3) }} 
                    {{ scope.row.type === 'distance' ? 'Å' : '°' }}
                  </template>
                </el-table-column>
                <el-table-column width="60">
                  <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-delete" @click="removeMeasurement(scope.$index)"></el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :md="8" :xs="24">
        <el-card class="info-card" shadow="hover">
          <h3>结构信息</h3>
          <div class="structure-details">
            <div class="detail-item">
              <span class="label">化学式:</span>
              <span class="value">{{ structure.formula }}</span>
            </div>
            <div class="detail-item">
              <span class="label">晶系:</span>
              <span class="value">{{ structure.crystalSystem }}</span>
            </div>
            <div class="detail-item">
              <span class="label">空间群:</span>
              <span class="value">{{ structure.spaceGroup }}</span>
            </div>
            <div class="detail-item">
              <span class="label">来源:</span>
              <span class="value">{{ structure.source }}</span>
            </div>
          </div>

          <h3>晶胞参数</h3>
          <div class="lattice-parameters">
            <el-row :gutter="10">
              <el-col :span="8">
                <div class="parameter-item">
                  <span class="param-label">a (Å):</span>
                  <span class="param-value">{{ structure.lattice.a.toFixed(3) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="parameter-item">
                  <span class="param-label">b (Å):</span>
                  <span class="param-value">{{ structure.lattice.b.toFixed(3) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="parameter-item">
                  <span class="param-label">c (Å):</span>
                  <span class="param-value">{{ structure.lattice.c.toFixed(3) }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="8">
                <div class="parameter-item">
                  <span class="param-label">α (°):</span>
                  <span class="param-value">{{ structure.lattice.alpha.toFixed(3) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="parameter-item">
                  <span class="param-label">β (°):</span>
                  <span class="param-value">{{ structure.lattice.beta.toFixed(3) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="parameter-item">
                  <span class="param-label">γ (°):</span>
                  <span class="param-value">{{ structure.lattice.gamma.toFixed(3) }}</span>
                </div>
              </el-col>
            </el-row>
          </div>

          <h3>原子坐标</h3>
          <el-table :data="structure.atoms" height="300" size="small" style="width: 100%">
            <el-table-column prop="element" label="元素" width="60"></el-table-column>
            <el-table-column prop="x" label="X" width="70">
              <template slot-scope="scope">{{ scope.row.x.toFixed(4) }}</template>
            </el-table-column>
            <el-table-column prop="y" label="Y" width="70">
              <template slot-scope="scope">{{ scope.row.y.toFixed(4) }}</template>
            </el-table-column>
            <el-table-column prop="z" label="Z" width="70">
              <template slot-scope="scope">{{ scope.row.z.toFixed(4) }}</template>
            </el-table-column>
            <el-table-column prop="occupancy" label="占位" width="60">
              <template slot-scope="scope">{{ scope.row.occupancy.toFixed(2) }}</template>
            </el-table-column>
          </el-table>

          <div class="export-section">
            <h3>导出结构</h3>
            <div class="export-buttons">
              <el-button type="primary" size="small" @click="exportStructure('cif')">CIF格式</el-button>
              <el-button type="primary" size="small" @click="exportStructure('xyz')">XYZ格式</el-button>
              <el-button type="primary" size="small" @click="exportStructure('pdb')">PDB格式</el-button>
            </div>
          </div>
        </el-card>
        
        <el-card class="element-info-card" shadow="hover" v-if="selectedElement">
          <h3>元素信息: {{ selectedElement.symbol }}</h3>
          <div class="element-details">
            <div class="element-header">
              <div class="element-symbol">{{ selectedElement.symbol }}</div>
              <div class="element-basic">
                <div class="element-name">{{ selectedElement.name }}</div>
                <div class="element-atomic-number">原子序数: {{ selectedElement.atomicNumber }}</div>
              </div>
            </div>
            
            <div class="element-properties">
              <div class="property-item">
                <span class="property-label">原子量:</span>
                <span class="property-value">{{ selectedElement.atomicMass }} u</span>
              </div>
              <div class="property-item">
                <span class="property-label">电子排布:</span>
                <span class="property-value">{{ selectedElement.electronConfiguration }}</span>
              </div>
              <div class="property-item">
                <span class="property-label">电负性:</span>
                <span class="property-value">{{ selectedElement.electronegativity }}</span>
              </div>
              <div class="property-item">
                <span class="property-label">原子半径:</span>
                <span class="property-value">{{ selectedElement.atomicRadius }} pm</span>
              </div>
              <div class="property-item">
                <span class="property-label">价态:</span>
                <span class="property-value">{{ selectedElement.valence }}</span>
              </div>
              <div class="property-item">
                <span class="property-label">熔点:</span>
                <span class="property-value">{{ selectedElement.meltingPoint }} K</span>
              </div>
              <div class="property-item">
                <span class="property-label">沸点:</span>
                <span class="property-value">{{ selectedElement.boilingPoint }} K</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
// 注：实际应用中，你需要引入适合的3D分子可视化库，如3Dmol.js、NGL Viewer或自己开发的WebGL组件
export default {
  name: 'StructureViewer',
  data() {
    return {
      structureId: '',
      structureLoaded: false,
      controlMode: 'rotate', // rotate, pan, zoom
      viewMode: 'ball-stick',
      showUnitCell: true,
      colorScheme: 'cpk',
      measurementActive: false,
      measurementType: 'distance',
      measurements: [],
      selectedAtom: null,
      selectedElement: null,
      cameraPosition: { x: 0, y: 0, z: 50 },
      
      // 模拟的结构数据，实际应从API获取
      structure: {
        id: 'mp-149',
        name: '硅晶体结构',
        formula: 'Si',
        crystalSystem: '立方晶系',
        spaceGroup: 'Fd-3m (227)',
        source: 'Materials Project',
        lattice: {
          a: 5.468, 
          b: 5.468, 
          c: 5.468, 
          alpha: 90, 
          beta: 90, 
          gamma: 90
        },
        atoms: [
          { element: 'Si', x: 0.000, y: 0.000, z: 0.000, occupancy: 1.0, index: 1 },
          { element: 'Si', x: 0.000, y: 0.500, z: 0.500, occupancy: 1.0, index: 2 },
          { element: 'Si', x: 0.500, y: 0.000, z: 0.500, occupancy: 1.0, index: 3 },
          { element: 'Si', x: 0.500, y: 0.500, z: 0.000, occupancy: 1.0, index: 4 },
          { element: 'Si', x: 0.250, y: 0.250, z: 0.250, occupancy: 1.0, index: 5 },
          { element: 'Si', x: 0.250, y: 0.750, z: 0.750, occupancy: 1.0, index: 6 },
          { element: 'Si', x: 0.750, y: 0.250, z: 0.750, occupancy: 1.0, index: 7 },
          { element: 'Si', x: 0.750, y: 0.750, z: 0.250, occupancy: 1.0, index: 8 }
        ]
      },
      
      // 元素数据示例
      elementData: {
        Si: {
          symbol: 'Si',
          name: '硅',
          atomicNumber: 14,
          atomicMass: 28.0855,
          electronConfiguration: '1s² 2s² 2p⁶ 3s² 3p²',
          electronegativity: 1.9,
          atomicRadius: 111,
          valence: '+4 (也可以是 -4)',
          meltingPoint: 1687,
          boilingPoint: 3538
        }
      }
    }
  },
  created() {
    // 从路由参数获取结构ID
    this.structureId = this.$route.params.id || 'mp-149'
  },
  mounted() {
    // 在实际场景中这里会加载3D可视化库并初始化查看器
    this.initViewer()
  },
  methods: {
    initViewer() {
      // 模拟加载可视化组件
      setTimeout(() => {
        this.structureLoaded = true
        
        // 在实际应用中，这里应该包含初始化3D查看器的代码
        console.log('3D查看器初始化完成')
        
        // 模拟选择一个原子的情况
        this.selectAtom(this.structure.atoms[0])
      }, 1000)
    },
    
    goBack() {
      this.$router.go(-1)
    },
    
    setControlMode(mode) {
      this.controlMode = mode
      // 在实际应用中，这里应该更新3D查看器的控制模式
      console.log(`控制模式更改为: ${mode}`)
    },
    
    handleViewChange(mode) {
      this.viewMode = mode
      // 在实际应用中，这里应该更新3D查看器的显示模式
      console.log(`视图模式更改为: ${mode}`)
    },
    
    handleUnitCellDisplay(option) {
      this.showUnitCell = option === 'show' || option === 'expand'
      // 在实际应用中，这里应该更新3D查看器的晶胞显示
      console.log(`晶胞显示选项: ${option}`)
    },
    
    handleColorScheme(scheme) {
      this.colorScheme = scheme
      // 在实际应用中，这里应该更新3D查看器的配色方案
      console.log(`配色方案更改为: ${scheme}`)
    },
    
    resetView() {
      // 在实际应用中，这里应该重置3D查看器的视角
      console.log('重置视图')
    },
    
    takeScreenshot() {
      // 在实际应用中，这里应该捕获3D查看器的当前帧作为图片
      this.$message({
        message: '截图已保存',
        type: 'success'
      })
    },
    
    toggleFullscreen() {
      // 在实际应用中，这里应该切换3D查看器的全屏显示
      console.log('切换全屏模式')
    },
    
    toggleMeasurementMode(active) {
      // 在实际应用中，这里应该更新3D查看器的测量模式
      console.log(`测量模式: ${active ? '开启' : '关闭'}`)
    },
    
    selectAtom(atom) {
      this.selectedAtom = atom
      this.selectedElement = this.elementData[atom.element]
      
      // 在实际应用中，这里应该在3D查看器中高亮显示所选原子
      console.log(`选中原子: ${atom.element}${atom.index}`)
    },
    
    removeMeasurement(index) {
      this.measurements.splice(index, 1)
      
      // 在实际应用中，这里应该从3D查看器中移除对应的测量标记
      console.log(`删除测量结果 #${index}`)
    },
    
    exportStructure(format) {
      // 在实际应用中，这里应该生成对应格式的结构文件并提供下载
      this.$message({
        message: `结构已导出为 ${format.toUpperCase()} 格式`,
        type: 'success'
      })
      
      // 模拟下载操作
      console.log(`导出${this.structure.formula}结构为${format}格式`)
    },
    
    formatCoord(value) {
      return value.toFixed(3)
    },
    
    getMeasurementTypeName(type) {
      const types = {
        'distance': '距离',
        'angle': '角度',
        'dihedral': '二面角'
      }
      return types[type] || type
    },
    
    getMeasurementTagType(type) {
      const types = {
        'distance': '',
        'angle': 'success',
        'dihedral': 'warning'
      }
      return types[type] || ''
    }
  }
}
</script>

<style scoped lang="scss">
.structure-viewer-container {
  padding: 20px;
  
  .header-section {
    margin-bottom: 20px;
    
    .back-button {
      display: inline-block;
      cursor: pointer;
      margin-bottom: 15px;
      color: #409EFF;
      font-size: 16px;
      
      &:hover {
        text-decoration: underline;
      }
      
      i {
        margin-right: 5px;
      }
    }
    
    .viewer-title {
      font-size: 28px;
      font-weight: 600;
      margin: 0 0 10px 0;
    }
    
    .structure-info {
      font-size: 16px;
      color: #909399;
    }
  }
  
  .viewer-card {
    margin-bottom: 20px;
    
    .viewer-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      
      .view-controls {
        display: flex;
        gap: 10px;
      }
      
      .active {
        background-color: #409EFF;
        color: white;
      }
    }
    
    .viewer-container {
      height: 500px;
      background-color: #f5f7fa;
      border-radius: 4px;
      position: relative;
      
      .loading-placeholder {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        
        .loading-text {
          margin-top: 10px;
          font-size: 16px;
          color: #909399;
        }
      }
    }
    
    .viewer-statusbar {
      margin-top: 10px;
      display: flex;
      justify-content: space-between;
      font-size: 13px;
      color: #606266;
    }
  }
  
  .measurement-card {
    margin-bottom: 20px;
    
    .card-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      
      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
      }
    }
    
    .measurement-tools {
      .measurement-instructions {
        margin: 15px 0;
        font-size: 14px;
        color: #606266;
      }
      
      .measurement-results {
        h4 {
          font-size: 16px;
          margin: 15px 0 10px 0;
        }
      }
    }
  }
  
  .info-card {
    margin-bottom: 20px;
    
    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-top: 0;
      margin-bottom: 15px;
      color: #303133;
    }
    
    .structure-details {
      margin-bottom: 20px;
      
      .detail-item {
        margin-bottom: 10px;
        
        .label {
          display: inline-block;
          width: 70px;
          font-weight: 500;
          color: #606266;
        }
        
        .value {
          color: #303133;
        }
      }
    }
    
    .lattice-parameters {
      margin-bottom: 20px;
      
      .parameter-item {
        margin-bottom: 10px;
        
        .param-label {
          font-weight: 500;
          color: #606266;
        }
        
        .param-value {
          margin-left: 5px;
          color: #303133;
        }
      }
    }
    
    .export-section {
      margin-top: 20px;
      
      .export-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
      }
    }
  }
  
  .element-info-card {
    margin-bottom: 20px;
    
    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-top: 0;
      margin-bottom: 15px;
      color: #303133;
    }
    
    .element-details {
      .element-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        
        .element-symbol {
          font-size: 36px;
          font-weight: bold;
          width: 60px;
          height: 60px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #409EFF;
          color: white;
          border-radius: 5px;
          margin-right: 15px;
        }
        
        .element-basic {
          .element-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
          }
          
          .element-atomic-number {
            font-size: 14px;
            color: #606266;
          }
        }
      }
      
      .element-properties {
        .property-item {
          margin-bottom: 10px;
          
          .property-label {
            display: inline-block;
            width: 80px;
            font-weight: 500;
            color: #606266;
          }
          
          .property-value {
            color: #303133;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .structure-viewer-container {
    .viewer-container {
      height: 350px !important;
    }
    
    .viewer-toolbar {
      flex-wrap: wrap;
      
      .view-controls, .view-actions {
        margin-top: 10px;
      }
    }
  }
}
</style>