2025-06-09 12:01:47 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 12:02:22 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 12:14:38 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 12:15:51 [INFO]: Logger test successful
2025-06-09 12:16:17 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 12:16:41 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 12:16:55 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 12:18:51 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 12:21:52 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 13:21:10 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 13:21:10 [INFO]: 开始启动测试服务器...
2025-06-09 13:21:10 [INFO]: 尝试连接数据库...
2025-06-09 13:21:10 [INFO]: 数据库连接测试成功
2025-06-09 13:21:10 [INFO]: 数据库连接池创建成功
2025-06-09 13:21:10 [INFO]: 数据库连接测试成功
2025-06-09 13:21:10 [INFO]: 数据库连接成功
2025-06-09 13:21:10 [INFO]: 尝试连接Redis...
2025-06-09 13:21:10 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:21:10 [INFO]: Redis重新连接中...
2025-06-09 13:21:10 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:21:10 [INFO]: Redis重新连接中...
2025-06-09 13:21:10 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:21:10 [INFO]: Redis重新连接中...
2025-06-09 13:21:10 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:21:11 [INFO]: Redis重新连接中...
2025-06-09 13:21:11 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:21:11 [INFO]: Redis重新连接中...
2025-06-09 13:21:11 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:21:11 [INFO]: Redis重新连接中...
2025-06-09 13:21:11 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:21:11 [INFO]: Redis重新连接中...
2025-06-09 13:21:11 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:21:12 [INFO]: Redis重新连接中...
2025-06-09 13:21:12 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:21:12 [INFO]: Redis重新连接中...
2025-06-09 13:21:12 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:21:13 [INFO]: Redis重新连接中...
2025-06-09 13:21:13 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:21:13 [INFO]: Redis重新连接中...
2025-06-09 13:21:13 [ERROR]: Redis重连次数超过限制，停止重连
2025-06-09 13:21:13 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:21:13 [WARN]: Redis连接失败，将使用内存缓存:
2025-06-09 13:21:13 [INFO]: Redis连接成功
2025-06-09 13:21:13 [INFO]: 准备在端口 8001 启动服务器...
2025-06-09 13:21:13 [INFO]: 测试服务器启动成功，端口: 8001
2025-06-09 13:21:13 [INFO]: 环境: development
2025-06-09 13:21:13 [INFO]: 健康检查: http://localhost:8001/health
2025-06-09 13:21:13 [INFO]: API测试: http://localhost:8001/api/test
2025-06-09 13:21:48 [INFO]: ::1 - - [09/Jun/2025:05:21:48 +0000] "GET /health HTTP/1.1" 200 120 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.22621.2506"
2025-06-09 13:22:02 [INFO]: ::1 - - [09/Jun/2025:05:22:02 +0000] "GET /api/test HTTP/1.1" 200 67 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.22621.2506"
2025-06-09 13:24:54 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 13:32:37 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 13:35:40 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 13:36:37 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 13:39:42 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 13:39:55 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 13:41:00 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 13:42:31 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 13:42:59 [INFO]: Logger test
2025-06-09 13:44:29 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 13:45:59 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 13:46:18 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 13:47:18 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 13:47:37 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 13:49:22 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 13:49:22 [INFO]: 开始启动测试服务器...
2025-06-09 13:49:22 [INFO]: 尝试连接数据库...
2025-06-09 13:49:22 [INFO]: 数据库连接测试成功
2025-06-09 13:49:22 [INFO]: 数据库连接池创建成功
2025-06-09 13:49:22 [INFO]: 数据库连接测试成功
2025-06-09 13:49:22 [INFO]: 数据库连接成功
2025-06-09 13:49:22 [INFO]: 尝试连接Redis...
2025-06-09 13:49:22 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:49:22 [INFO]: Redis重新连接中...
2025-06-09 13:49:22 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:49:22 [INFO]: Redis重新连接中...
2025-06-09 13:49:22 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:49:22 [INFO]: Redis重新连接中...
2025-06-09 13:49:22 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:49:23 [INFO]: Redis重新连接中...
2025-06-09 13:49:23 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:49:23 [INFO]: Redis重新连接中...
2025-06-09 13:49:23 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:49:23 [INFO]: Redis重新连接中...
2025-06-09 13:49:23 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:49:23 [INFO]: Redis重新连接中...
2025-06-09 13:49:23 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:49:24 [INFO]: Redis重新连接中...
2025-06-09 13:49:24 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:49:24 [INFO]: Redis重新连接中...
2025-06-09 13:49:24 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:49:25 [INFO]: Redis重新连接中...
2025-06-09 13:49:25 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:49:25 [INFO]: Redis重新连接中...
2025-06-09 13:49:25 [ERROR]: Redis重连次数超过限制，停止重连
2025-06-09 13:49:25 [ERROR]: Redis连接错误:
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1139:18)
    at afterConnectMultiple (node:net:1714:7)
{
  "code": "ECONNREFUSED"
}
2025-06-09 13:49:25 [WARN]: Redis连接失败，将使用内存缓存:
2025-06-09 13:49:25 [INFO]: Redis连接成功
2025-06-09 13:49:25 [INFO]: 准备在端口 8001 启动服务器...
2025-06-09 13:49:25 [INFO]: 测试服务器启动成功，端口: 8001
2025-06-09 13:49:25 [INFO]: 环境: development
2025-06-09 13:49:25 [INFO]: 健康检查: http://localhost:8001/health
2025-06-09 13:49:25 [INFO]: API测试: http://localhost:8001/api/test
2025-06-09 13:49:30 [INFO]: ::1 - - [09/Jun/2025:05:49:30 +0000] "GET /health HTTP/1.1" 200 119 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.22621.2506"
2025-06-09 13:50:49 [INFO]: ::1 - - [09/Jun/2025:05:50:49 +0000] "GET /api/questions?limit=6&_t=1749448249098 HTTP/1.1" 404 152 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-06-09 13:51:31 [INFO]: ::1 - - [09/Jun/2025:05:51:31 +0000] "POST /api/auth/login HTTP/1.1" 404 154 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-06-09 13:52:30 [INFO]: ::1 - - [09/Jun/2025:05:52:30 +0000] "GET /health HTTP/1.1" 200 121 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.22621.2506"
2025-06-09 13:56:51 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 13:58:30 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 13:58:47 [INFO]: 使用内存缓存模式，这仅适用于开发环境
2025-06-09 13:59:40 [INFO]: 使用内存缓存模式，这仅适用于开发环境
