<template>
  <div class="downloads-container">
    <div class="page-header">
      <h1>我的下载</h1>
      <p>您已下载的资源和数据</p>
    </div>

    <el-card class="downloads-card" shadow="hover">
      <div slot="header" class="filter-section">
        <el-input
          placeholder="搜索下载记录"
          prefix-icon="el-icon-search"
          v-model="searchQuery"
          clearable
        ></el-input>
        <el-select v-model="filterType" placeholder="类型" clearable>
          <el-option label="所有类型" value=""></el-option>
          <el-option label="数据文件" value="data"></el-option>
          <el-option label="晶体结构" value="structure"></el-option>
          <el-option label="文献资料" value="literature"></el-option>
          <el-option label="软件工具" value="software"></el-option>
        </el-select>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </div>

      <el-table 
        :data="filteredDownloads" 
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column label="文件名" prop="fileName" min-width="220">
          <template slot-scope="scope">
            <div class="file-info">
              <i :class="getFileIconClass(scope.row.fileType)"></i>
              <div class="file-details">
                <div class="file-name">{{ scope.row.fileName }}</div>
                <div class="file-meta">{{ scope.row.fileSize }} | {{ scope.row.fileType }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="来源" prop="source" width="180"></el-table-column>
        <el-table-column label="下载日期" prop="downloadDate" width="180">
          <template slot-scope="scope">
            {{ formatDate(scope.row.downloadDate) }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button 
              type="primary" 
              size="mini" 
              icon="el-icon-download" 
              @click="downloadFile(scope.row)"
            >重新下载</el-button>
            <el-button 
              type="danger" 
              size="mini" 
              icon="el-icon-delete"
              @click="confirmDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalItems"
        ></el-pagination>
      </div>
    </el-card>

    <el-card class="storage-card" shadow="hover">
      <div class="storage-info">
        <h3>存储空间使用情况</h3>
        <el-progress :percentage="storageUsage.percentage" :format="formatStoragePercentage"></el-progress>
        <div class="storage-details">
          <span>已用: {{ formatStorageSize(storageUsage.used) }}</span>
          <span>总计: {{ formatStorageSize(storageUsage.total) }}</span>
        </div>
      </div>
      
      <div class="storage-chart">
        <h3>按文件类型划分</h3>
        <!-- 这里可以放置饼图或其他图表 -->
        <div class="chart-placeholder">
          文件类型分布图表将在这里显示
        </div>
      </div>
    </el-card>

    <!-- 确认删除对话框 -->
    <el-dialog
      title="确认删除"
      :visible.sync="deleteDialogVisible"
      width="30%"
    >
      <span>确定要删除文件 "{{ selectedFile ? selectedFile.fileName : '' }}" 吗？此操作无法撤销。</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="deleteFile">确定删除</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Downloads',
  data() {
    return {
      loading: false,
      searchQuery: '',
      filterType: '',
      dateRange: '',
      currentPage: 1,
      pageSize: 10,
      totalItems: 0,
      deleteDialogVisible: false,
      selectedFile: null,
      storageUsage: {
        used: 1.7 * 1024 * 1024 * 1024, // 1.7GB
        total: 5 * 1024 * 1024 * 1024,  // 5GB
        percentage: 34
      },
      downloads: [
        {
          id: 1,
          fileName: 'silicon_crystal_structure.cif',
          fileSize: '128 KB',
          fileType: 'structure',
          source: '晶体结构数据库',
          downloadDate: '2023-06-15T08:30:00',
          status: '已完成'
        },
        {
          id: 2,
          fileName: 'thermal_properties_dataset_2023.csv',
          fileSize: '2.4 MB',
          fileType: 'data',
          source: '材料热力学数据库',
          downloadDate: '2023-06-10T14:15:00',
          status: '已完成'
        },
        {
          id: 3,
          fileName: 'advanced_materials_research_vol_28.pdf',
          fileSize: '8.7 MB',
          fileType: 'literature',
          source: '文献资源库',
          downloadDate: '2023-06-05T09:45:00',
          status: '已完成'
        },
        {
          id: 4,
          fileName: 'material_property_calculator_v3.2.zip',
          fileSize: '45.2 MB',
          fileType: 'software',
          source: '软件工具库',
          downloadDate: '2023-05-28T16:20:00',
          status: '已完成'
        },
        {
          id: 5,
          fileName: 'composite_materials_database.json',
          fileSize: '16.8 MB',
          fileType: 'data',
          source: '复合材料数据库',
          downloadDate: '2023-05-22T11:10:00',
          status: '已完成'
        },
        {
          id: 6,
          fileName: 'ceramic_structures_collection.zip',
          fileSize: '78.5 MB',
          fileType: 'structure',
          source: '陶瓷材料数据库',
          downloadDate: '2023-05-15T13:40:00',
          status: '已完成'
        },
        {
          id: 7,
          fileName: 'polymer_simulation_results.xyz',
          fileSize: '34.1 MB',
          fileType: 'structure',
          source: '聚合物数据库',
          downloadDate: '2023-05-10T09:30:00',
          status: '已完成'
        },
        {
          id: 8,
          fileName: 'materials_science_journal_2023.pdf',
          fileSize: '12.3 MB',
          fileType: 'literature',
          source: '文献资源库',
          downloadDate: '2023-05-05T16:15:00',
          status: '已完成'
        }
      ]
    }
  },
  computed: {
    filteredDownloads() {
      let result = this.downloads;
      
      // 搜索过滤
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        result = result.filter(item => 
          item.fileName.toLowerCase().includes(query) || 
          item.source.toLowerCase().includes(query)
        );
      }
      
      // 类型过滤
      if (this.filterType) {
        result = result.filter(item => item.fileType === this.filterType);
      }
      
      // 日期过滤
      if (this.dateRange && this.dateRange[0] && this.dateRange[1]) {
        const startDate = new Date(this.dateRange[0]);
        const endDate = new Date(this.dateRange[1]);
        
        result = result.filter(item => {
          const downloadDate = new Date(item.downloadDate);
          return downloadDate >= startDate && downloadDate <= endDate;
        });
      }
      
      // 分页处理
      this.totalItems = result.length;
      return result.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize);
    }
  },
  created() {
    this.fetchDownloads();
  },
  methods: {
    fetchDownloads() {
      this.loading = true;
      
      // 模拟API请求延迟
      setTimeout(() => {
        // 在实际项目中，这里会是真实的API调用
        // this.downloads = response.data;
        this.loading = false;
      }, 500);
    },
    
    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    
    getStatusType(status) {
      const types = {
        '已完成': 'success',
        '下载中': 'primary',
        '已失效': 'info',
        '下载失败': 'danger'
      };
      return types[status] || '';
    },
    
    getFileIconClass(fileType) {
      const icons = {
        'structure': 'el-icon-s-grid',
        'data': 'el-icon-document',
        'literature': 'el-icon-reading',
        'software': 'el-icon-cpu'
      };
      return icons[fileType] || 'el-icon-document';
    },
    
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
    },
    
    handleCurrentChange(page) {
      this.currentPage = page;
    },
    
    downloadFile(file) {
      // 实际项目中，这里会调用下载API
      this.$message({
        message: `正在下载文件：${file.fileName}`,
        type: 'success'
      });
    },
    
    confirmDelete(file) {
      this.selectedFile = file;
      this.deleteDialogVisible = true;
    },
    
    deleteFile() {
      // 实际项目中，这里会调用删除API
      if (this.selectedFile) {
        this.downloads = this.downloads.filter(item => item.id !== this.selectedFile.id);
        this.$message({
          message: `文件 ${this.selectedFile.fileName} 已成功删除`,
          type: 'success'
        });
        this.deleteDialogVisible = false;
        this.selectedFile = null;
      }
    },
    
    formatStoragePercentage(percentage) {
      return `${percentage}%`;
    },
    
    formatStorageSize(bytes) {
      if (bytes === 0) return '0 B';
      
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
  }
}
</script>

<style scoped lang="scss">
.downloads-container {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    h1 {
      font-size: 28px;
      font-weight: 600;
      margin: 0 0 10px 0;
    }
    
    p {
      font-size: 16px;
      color: #909399;
      margin: 0;
    }
  }
  
  .downloads-card {
    margin-bottom: 20px;
    
    .filter-section {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 20px;
      
      .el-input {
        width: 250px;
      }
    }
    
    .file-info {
      display: flex;
      align-items: center;
      
      i {
        font-size: 24px;
        margin-right: 10px;
        color: #409EFF;
      }
      
      .file-details {
        .file-name {
          font-weight: 500;
        }
        
        .file-meta {
          font-size: 12px;
          color: #909399;
          margin-top: 3px;
        }
      }
    }
    
    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
  
  .storage-card {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    
    .storage-info {
      flex: 1;
      min-width: 250px;
      margin-right: 20px;
      
      h3 {
        font-size: 18px;
        margin-top: 0;
        margin-bottom: 15px;
      }
      
      .storage-details {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
        font-size: 14px;
        color: #606266;
      }
    }
    
    .storage-chart {
      flex: 1;
      min-width: 250px;
      
      h3 {
        font-size: 18px;
        margin-top: 0;
        margin-bottom: 15px;
      }
      
      .chart-placeholder {
        height: 150px;
        background-color: #f5f7fa;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #909399;
        border-radius: 4px;
      }
    }
  }
}

@media (max-width: 768px) {
  .downloads-container {
    .filter-section {
      .el-input, .el-select, .el-date-picker {
        width: 100% !important;
      }
    }
    
    .storage-card {
      .storage-info, .storage-chart {
        width: 100%;
        margin-right: 0;
        margin-bottom: 20px;
      }
    }
  }
}
</style>