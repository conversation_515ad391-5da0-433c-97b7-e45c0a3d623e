// 导入变量
@import './variables.scss';

// 重置样式
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 14px;
  line-height: 1.6;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: var(--el-text-color-primary);
  background-color: var(--el-bg-color);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 链接样式
a {
  color: var(--el-color-primary);
  text-decoration: none;
  transition: color 0.3s ease;

  &:hover {
    color: var(--el-color-primary-light-3);
  }
}

// 图片样式
img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}

// 按钮样式
button {
  cursor: pointer;
  border: none;
  outline: none;
  background: transparent;
  font-family: inherit;
}

// 输入框样式
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
  outline: none;
}

// 列表样式
ul, ol {
  list-style: none;
}

// 表格样式
table {
  border-collapse: collapse;
  border-spacing: 0;
}

// 工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// 响应式工具类
@media (max-width: 767px) {
  .hidden-mobile {
    display: none !important;
  }
}

@media (min-width: 768px) {
  .hidden-desktop {
    display: none !important;
  }
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(20px);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}

// 自定义滚动条
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-fill-color-dark);
  border-radius: 4px;
  
  &:hover {
    background: var(--el-fill-color-darker);
  }
}

// Element Plus 自定义样式
.el-button {
  font-weight: 500;
}

.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-dialog {
  border-radius: 8px;
}

.el-message {
  border-radius: 6px;
}

// 页面布局
.page-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: 20px;
  
  @media (max-width: 767px) {
    padding: 10px;
  }
}

// 加载状态
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  
  .loading-text {
    margin-left: 10px;
    color: var(--el-text-color-regular);
  }
}

// 空状态
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: var(--el-text-color-regular);
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }
  
  .empty-text {
    font-size: 16px;
    margin-bottom: 8px;
  }
  
  .empty-description {
    font-size: 14px;
    opacity: 0.7;
  }
}

// 错误状态
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: var(--el-color-danger);
  
  .error-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .error-text {
    font-size: 16px;
    margin-bottom: 8px;
  }
  
  .error-description {
    font-size: 14px;
    opacity: 0.7;
  }
}
