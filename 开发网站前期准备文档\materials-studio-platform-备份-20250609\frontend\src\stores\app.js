import { defineStore } from 'pinia'
import { ref, computed, readonly, watch } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 状态
  const isDarkMode = ref(false)
  const isGlobalLoading = ref(false)
  const loadingText = ref('加载中...')
  const showCustomerService = ref(true)
  const isNetworkOnline = ref(navigator.onLine)
  const viewport = ref({
    width: window.innerWidth,
    height: window.innerHeight,
    isMobile: window.innerWidth < 768
  })

  // 侧边栏状态
  const sidebarCollapsed = ref(false)
  
  // 搜索状态
  const showSearch = ref(false)
  
  // 通知状态
  const notifications = ref([])
  const unreadCount = ref(0)

  // 计算属性
  const isMobile = computed(() => viewport.value.isMobile)
  const isTablet = computed(() => viewport.value.width >= 768 && viewport.value.width < 1024)
  const isDesktop = computed(() => viewport.value.width >= 1024)

  // 方法
  const toggleDarkMode = () => {
    isDarkMode.value = !isDarkMode.value
    localStorage.setItem('darkMode', isDarkMode.value.toString())
    
    // 更新HTML类名
    const html = document.documentElement
    if (isDarkMode.value) {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
  }

  const setGlobalLoading = (loading, text = '加载中...') => {
    isGlobalLoading.value = loading
    loadingText.value = text
  }

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
    localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value.toString())
  }

  const setSidebarCollapsed = (collapsed) => {
    sidebarCollapsed.value = collapsed
    localStorage.setItem('sidebarCollapsed', collapsed.toString())
  }

  const toggleSearch = () => {
    showSearch.value = !showSearch.value
  }

  const setNetworkStatus = (online) => {
    isNetworkOnline.value = online
  }

  const updateViewport = () => {
    viewport.value = {
      width: window.innerWidth,
      height: window.innerHeight,
      isMobile: window.innerWidth < 768
    }
  }

  const addNotification = (notification) => {
    const id = Date.now().toString()
    const newNotification = {
      id,
      ...notification,
      timestamp: new Date(),
      read: false
    }
    
    notifications.value.unshift(newNotification)
    unreadCount.value++
    
    // 自动移除通知（可选）
    if (notification.autoRemove !== false) {
      setTimeout(() => {
        removeNotification(id)
      }, notification.duration || 5000)
    }
    
    return id
  }

  const removeNotification = (id) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      const notification = notifications.value[index]
      if (!notification.read) {
        unreadCount.value--
      }
      notifications.value.splice(index, 1)
    }
  }

  const markNotificationAsRead = (id) => {
    const notification = notifications.value.find(n => n.id === id)
    if (notification && !notification.read) {
      notification.read = true
      unreadCount.value--
    }
  }

  const markAllNotificationsAsRead = () => {
    notifications.value.forEach(notification => {
      notification.read = true
    })
    unreadCount.value = 0
  }

  const clearNotifications = () => {
    notifications.value = []
    unreadCount.value = 0
  }

  const closeAllModals = () => {
    showSearch.value = false
    // 可以添加其他模态框的关闭逻辑
  }

  const initializeApp = async () => {
    try {
      // 从localStorage恢复设置
      const savedDarkMode = localStorage.getItem('darkMode')
      if (savedDarkMode !== null) {
        isDarkMode.value = savedDarkMode === 'true'
      } else {
        // 检测系统主题偏好
        isDarkMode.value = window.matchMedia('(prefers-color-scheme: dark)').matches
      }

      const savedSidebarCollapsed = localStorage.getItem('sidebarCollapsed')
      if (savedSidebarCollapsed !== null) {
        sidebarCollapsed.value = savedSidebarCollapsed === 'true'
      }

      // 应用主题
      const html = document.documentElement
      if (isDarkMode.value) {
        html.classList.add('dark')
      } else {
        html.classList.remove('dark')
      }

      // 监听系统主题变化
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      mediaQuery.addEventListener('change', (e) => {
        if (localStorage.getItem('darkMode') === null) {
          isDarkMode.value = e.matches
          if (e.matches) {
            html.classList.add('dark')
          } else {
            html.classList.remove('dark')
          }
        }
      })

      // 更新视口信息
      updateViewport()

    } catch (error) {
      console.error('应用初始化失败:', error)
    }
  }

  return {
    // 状态
    isDarkMode: readonly(isDarkMode),
    isGlobalLoading: readonly(isGlobalLoading),
    loadingText: readonly(loadingText),
    showCustomerService: readonly(showCustomerService),
    isNetworkOnline: readonly(isNetworkOnline),
    viewport: readonly(viewport),
    sidebarCollapsed: readonly(sidebarCollapsed),
    showSearch: readonly(showSearch),
    notifications: readonly(notifications),
    unreadCount: readonly(unreadCount),

    // 计算属性
    isMobile,
    isTablet,
    isDesktop,

    // 方法
    toggleDarkMode,
    setGlobalLoading,
    toggleSidebar,
    setSidebarCollapsed,
    toggleSearch,
    setNetworkStatus,
    updateViewport,
    addNotification,
    removeNotification,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    clearNotifications,
    closeAllModals,
    initializeApp
  }
})
