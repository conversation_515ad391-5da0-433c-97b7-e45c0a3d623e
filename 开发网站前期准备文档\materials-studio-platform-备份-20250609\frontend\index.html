<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Materials Studio答疑平台 - 专业的分子模拟软件问答社区" />
    <meta name="keywords" content="Materials Studio, 分子模拟, 问答平台, 科研工具" />
    <meta name="author" content="Materials Studio Platform Team" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://materialsstudio-qa.com/" />
    <meta property="og:title" content="Materials Studio答疑平台" />
    <meta property="og:description" content="专业的分子模拟软件问答社区，为研究人员提供技术支持和学习资源" />
    <meta property="og:image" content="/og-image.jpg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://materialsstudio-qa.com/" />
    <meta property="twitter:title" content="Materials Studio答疑平台" />
    <meta property="twitter:description" content="专业的分子模拟软件问答社区，为研究人员提供技术支持和学习资源" />
    <meta property="twitter:image" content="/og-image.jpg" />

    <!-- Preload critical resources -->
    <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossorigin />
    
    <!-- Theme color -->
    <meta name="theme-color" content="#409eff" />
    
    <!-- PWA -->
    <link rel="manifest" href="/manifest.json" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Materials Studio Platform" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />

    <title>Materials Studio答疑平台</title>
    
    <!-- Loading styles -->
    <style>
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.3s ease;
      }
      
      .loading-content {
        text-align: center;
        color: white;
      }
      
      .loading-logo {
        width: 80px;
        height: 80px;
        margin: 0 auto 20px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        animation: pulse 2s infinite;
      }
      
      .loading-text {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 10px;
      }
      
      .loading-subtitle {
        font-size: 14px;
        opacity: 0.8;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        margin: 20px auto;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .fade-out {
        opacity: 0;
        pointer-events: none;
      }
    </style>
  </head>
  <body>
    <!-- Loading screen -->
    <div id="loading">
      <div class="loading-content">
        <div class="loading-logo">🧪</div>
        <div class="loading-text">Materials Studio Platform</div>
        <div class="loading-subtitle">专业的分子模拟问答平台</div>
        <div class="loading-spinner"></div>
      </div>
    </div>
    
    <!-- App container -->
    <div id="app"></div>
    
    <!-- Main script -->
    <script type="module" src="/src/main.js"></script>
    
    <!-- Remove loading screen when app is ready -->
    <script>
      window.addEventListener('load', () => {
        setTimeout(() => {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.classList.add('fade-out');
            setTimeout(() => {
              loading.remove();
            }, 300);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
