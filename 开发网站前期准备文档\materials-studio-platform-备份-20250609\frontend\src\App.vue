<template>
  <div id="app" :class="{ 'dark-mode': isDarkMode }">
    <!-- 全局加载遮罩 -->
    <div v-if="isGlobalLoading" class="global-loading">
      <div class="loading-content">
        <el-icon class="loading-icon" :size="40">
          <Loading />
        </el-icon>
        <p class="loading-text">{{ loadingText }}</p>
      </div>
    </div>

    <!-- 主要内容 -->
    <router-view v-slot="{ Component, route }">
      <transition
        :name="route.meta.transition || 'fade'"
        mode="out-in"
        appear
      >
        <component :is="Component" :key="route.path" />
      </transition>
    </router-view>

    <!-- 全局通知 -->
    <GlobalNotification />

    <!-- 返回顶部按钮 -->
    <BackToTop />

    <!-- 客服聊天 -->
    <CustomerService v-if="showCustomerService" />
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted } from 'vue'
import { useAppStore } from '@/stores/app'
import { useAuthStore } from '@/stores/auth'
import GlobalNotification from '@/components/common/GlobalNotification.vue'
import BackToTop from '@/components/common/BackToTop.vue'
import CustomerService from '@/components/common/CustomerService.vue'

const appStore = useAppStore()
const authStore = useAuthStore()

// 计算属性
const isDarkMode = computed(() => appStore.isDarkMode)
const isGlobalLoading = computed(() => appStore.isGlobalLoading)
const loadingText = computed(() => appStore.loadingText)
const showCustomerService = computed(() => appStore.showCustomerService)

// 生命周期
onMounted(async () => {
  // 初始化应用
  await initializeApp()
  
  // 监听网络状态
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
  
  // 监听键盘快捷键
  window.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  window.removeEventListener('online', handleOnline)
  window.removeEventListener('offline', handleOffline)
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('keydown', handleKeydown)
})

// 方法
const initializeApp = async () => {
  try {
    appStore.setGlobalLoading(true, '正在初始化应用...')
    
    // 初始化认证状态
    await authStore.initializeAuth()
    
    // 初始化应用设置
    await appStore.initializeApp()
    
    // 检查浏览器兼容性
    checkBrowserCompatibility()
    
    // 设置主题
    applyTheme()
    
  } catch (error) {
    console.error('应用初始化失败:', error)
    ElMessage.error('应用初始化失败，请刷新页面重试')
  } finally {
    appStore.setGlobalLoading(false)
  }
}

const checkBrowserCompatibility = () => {
  const isModernBrowser = 'fetch' in window && 'Promise' in window
  if (!isModernBrowser) {
    ElMessageBox.alert(
      '您的浏览器版本过低，可能无法正常使用本网站的所有功能。建议升级到最新版本的Chrome、Firefox或Safari。',
      '浏览器兼容性提示',
      {
        confirmButtonText: '我知道了',
        type: 'warning'
      }
    )
  }
}

const applyTheme = () => {
  const html = document.documentElement
  if (isDarkMode.value) {
    html.classList.add('dark')
  } else {
    html.classList.remove('dark')
  }
}

const handleOnline = () => {
  ElMessage.success('网络连接已恢复')
  appStore.setNetworkStatus(true)
}

const handleOffline = () => {
  ElMessage.warning('网络连接已断开')
  appStore.setNetworkStatus(false)
}

const handleResize = () => {
  appStore.updateViewport()
}

const handleKeydown = (event) => {
  // Ctrl/Cmd + K 打开搜索
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault()
    appStore.toggleSearch()
  }
  
  // Esc 关闭模态框
  if (event.key === 'Escape') {
    appStore.closeAllModals()
  }
}

// 监听主题变化
watch(isDarkMode, (newValue) => {
  applyTheme()
}, { immediate: true })
</script>

<style lang="scss">
#app {
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--el-text-color-primary);
  background-color: var(--el-bg-color);
  transition: all 0.3s ease;
}

// 全局加载遮罩
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  
  .dark-mode & {
    background: rgba(0, 0, 0, 0.9);
  }
  
  .loading-content {
    text-align: center;
    
    .loading-icon {
      animation: spin 1s linear infinite;
      color: var(--el-color-primary);
      margin-bottom: 16px;
    }
    
    .loading-text {
      font-size: 14px;
      color: var(--el-text-color-regular);
      margin: 0;
    }
  }
}

// 页面过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.3s ease;
}

.slide-left-enter-from {
  transform: translateX(30px);
  opacity: 0;
}

.slide-left-leave-to {
  transform: translateX(-30px);
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(30px);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-30px);
  opacity: 0;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-fill-color-dark);
  border-radius: 4px;
  
  &:hover {
    background: var(--el-fill-color-darker);
  }
}

// 响应式断点
@media (max-width: 768px) {
  #app {
    font-size: 14px;
  }
}

// 动画关键帧
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}
</style>
