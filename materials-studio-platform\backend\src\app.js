const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const { createServer } = require('http');
const { Server } = require('socket.io');
require('dotenv').config();

const config = require('./config');
const logger = require('./utils/logger');
// 尝试导入数据库模块，如果失败则使用内存数据库模式
let database;
try {
  database = require('./database/connection');
} catch (err) {
  logger.warn('数据库模块加载失败，将使用内存数据库模式');
  database = require('./database/memory-db');
}
// 尝试导入Redis模块，如果失败则使用内存缓存
let redis;
try {
  redis = require('./utils/redis');
} catch (err) {
  logger.warn('Redis模块加载失败，将使用内存缓存');
  redis = require('./utils/memory-cache');
}
const errorHandler = require('./middleware/errorHandler');
const notFound = require('./middleware/notFound');

// 导入路由 - 逐步测试
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const categoryRoutes = require('./routes/categories');
const adminRoutes = require('./routes/admin');
// const questionRoutes = require('./routes/questions');
// const answerRoutes = require('./routes/answers');
// const tagRoutes = require('./routes/tags');
// const resourceRoutes = require('./routes/resources');
// const membershipRoutes = require('./routes/memberships');
// const orderRoutes = require('./routes/orders');
// const uploadRoutes = require('./routes/upload');
// const notificationRoutes = require('./routes/notifications');
// const databasesRoutes = require('./routes/databases');

const app = express();
const server = createServer(app);

// Socket.IO配置
const io = new Server(server, {
  cors: {
    origin: config.cors.origin,
    credentials: config.cors.credentials
  }
});

// 全局中间件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
}));

app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: config.cors.credentials,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 日志中间件
if (config.app.env !== 'test') {
  app.use(morgan('combined', {
    stream: {
      write: (message) => logger.info(message.trim())
    }
  }));
}

// 速率限制
const limiter = rateLimit({
  windowMs: config.security.rateLimitWindowMs,
  max: config.security.rateLimitMaxRequests,
  message: {
    error: '请求过于频繁，请稍后再试',
    code: 429
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const speedLimiter = slowDown({
  windowMs: config.security.slowDownWindowMs,
  delayAfter: config.security.slowDownDelayAfter,
  delayMs: config.security.slowDownDelayMs
});

app.use('/api/', limiter);
app.use('/api/', speedLimiter);

// 静态文件服务
app.use('/uploads', express.static('uploads'));

// 健康检查
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: config.app.env,
    version: process.env.npm_package_version || '1.0.0'
  });
});

// API路由 - 逐步测试
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/categories', categoryRoutes);
app.use('/api/admin', adminRoutes);
// app.use('/api/questions', questionRoutes);
// app.use('/api/answers', answerRoutes);
// app.use('/api/tags', tagRoutes);
// app.use('/api/resources', resourceRoutes);
// app.use('/api/memberships', membershipRoutes);
// app.use('/api/orders', orderRoutes);
// app.use('/api/upload', uploadRoutes);
// app.use('/api/notifications', notificationRoutes);
// app.use('/api/databases', databasesRoutes);

// Socket.IO事件处理
io.on('connection', (socket) => {
  logger.info(`用户连接: ${socket.id}`);

  // 用户加入房间
  socket.on('join', (data) => {
    if (data.userId) {
      socket.join(`user_${data.userId}`);
      logger.info(`用户 ${data.userId} 加入房间`);
    }
  });

  // 用户离开
  socket.on('disconnect', () => {
    logger.info(`用户断开连接: ${socket.id}`);
  });
});

// 将io实例添加到app中，供其他模块使用
app.set('io', io);

// 错误处理中间件
app.use(notFound);
app.use(errorHandler);

// 启动服务器
const PORT = config.app.port || 8000;

async function startServer() {
  try {
    console.log('开始启动服务器...');
    logger.info('开始启动服务器...');

    // 尝试连接数据库（可选）
    try {
      console.log('尝试连接数据库...');
      logger.info('尝试连接数据库...');
      await database.testConnection();
      console.log('数据库连接成功');
      logger.info('数据库连接成功');
    } catch (dbError) {
      console.warn('数据库连接失败，将使用内存数据库模式:', dbError.message);
      logger.warn('数据库连接失败，将使用内存数据库模式:', dbError.message);
    }

    // 尝试连接Redis（可选）
    try {
      console.log('尝试连接Redis...');
      logger.info('尝试连接Redis...');
      await redis.ping();
      console.log('Redis连接成功');
      logger.info('Redis连接成功');
    } catch (redisError) {
      console.warn('Redis连接失败，将使用内存缓存模式:', redisError.message);
      logger.warn('Redis连接失败，将使用内存缓存模式:', redisError.message);
    }

    // 启动服务器
    console.log(`准备在端口 ${PORT} 启动服务器...`);
    logger.info(`准备在端口 ${PORT} 启动服务器...`);

    return new Promise((resolve, reject) => {
      server.listen(PORT, (err) => {
        if (err) {
          console.error('服务器启动失败:', err);
          logger.error('服务器启动失败:', err);
          reject(err);
          return;
        }

        console.log(`服务器启动成功，端口: ${PORT}`);
        console.log(`环境: ${config.app.env}`);
        console.log(`前端地址: http://localhost:3000`);
        console.log(`后端地址: http://localhost:${PORT}`);
        console.log(`健康检查: http://localhost:${PORT}/health`);

        logger.info(`服务器启动成功，端口: ${PORT}`);
        logger.info(`环境: ${config.app.env}`);
        logger.info(`前端地址: http://localhost:3000`);
        logger.info(`后端地址: http://localhost:${PORT}`);
        logger.info(`健康检查: http://localhost:${PORT}/health`);

        resolve();
      });
    });

    server.on('error', (error) => {
      console.error('服务器错误:', error);
      logger.error('服务器错误:', error);
      if (error.code === 'EADDRINUSE') {
        console.error(`端口 ${PORT} 已被占用`);
        logger.error(`端口 ${PORT} 已被占用`);
      }
    });

  } catch (error) {
    console.error('服务器启动失败:', error);
    logger.error('服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', async () => {
  logger.info('收到SIGTERM信号，开始优雅关闭...');
  
  server.close(async () => {
    logger.info('HTTP服务器已关闭');
    
    try {
      await database.close();
      logger.info('数据库连接已关闭');
      
      if (redis) {
        await redis.quit();
        logger.info('Redis连接已关闭');
      }
    } catch (error) {
      logger.error('关闭连接时出错:', error);
    }
    
    process.exit(0);
  });
});

// 导出app对象和启动函数
module.exports = { app, startServer };

// 直接启动服务器（当直接运行此文件时）
if (require.main === module) {
  startServer().catch(error => {
    console.error('启动服务器时发生错误:', error);
    logger.error('启动服务器时发生错误:', error);
    process.exit(1);
  });
}
