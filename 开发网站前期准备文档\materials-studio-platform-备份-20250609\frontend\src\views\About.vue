<template>
  <div class="about-container">
    <div class="page-header">
      <h1>关于我们</h1>
      <p>材料科学数据与分析平台</p>
    </div>

    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="introduction-card" shadow="hover">
          <h2>平台介绍</h2>
          <p>Materials Studio Platform是一个专注于材料科学领域的综合性平台，致力于为科研人员、工程师和学生提供高质量的材料数据、分析工具和专业交流环境。</p>
          
          <p>我们的平台集成了多种专业数据库、高级搜索功能、数据分析工具和学术交流社区，支持用户进行材料性能比较、结构分析和数据可视化，助力材料科学研究和工程应用。</p>

          <h3>平台特色</h3>
          <div class="feature-list">
            <div class="feature-item">
              <i class="el-icon-data-analysis"></i>
              <div class="feature-content">
                <h4>多源数据库整合</h4>
                <p>整合国内外知名材料数据库，涵盖结构数据、性能数据和工业材料信息</p>
              </div>
            </div>
            
            <div class="feature-item">
              <i class="el-icon-search"></i>
              <div class="feature-content">
                <h4>智能搜索系统</h4>
                <p>支持文本、结构和性能多维度搜索，精准定位所需材料数据</p>
              </div>
            </div>
            
            <div class="feature-item">
              <i class="el-icon-data-line"></i>
              <div class="feature-content">
                <h4>交互式数据分析</h4>
                <p>可视化分析工具，支持多材料性能对比和相关性分析</p>
              </div>
            </div>
            
            <div class="feature-item">
              <i class="el-icon-chat-dot-round"></i>
              <div class="feature-content">
                <h4>专业问答社区</h4>
                <p>连接材料科学领域专家，提供专业技术问答和学术交流</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mission-section">
      <el-col :md="12" :xs="24">
        <el-card shadow="hover">
          <h2>我们的使命</h2>
          <p>打造最专业、最全面的材料科学数据服务平台，推动材料科学研究发展和技术创新。</p>
          <ul class="mission-list">
            <li>促进材料数据的开放共享和高效利用</li>
            <li>提供先进的数据分析工具，支持材料科学研究</li>
            <li>构建专业的学术交流社区，推动知识传播</li>
            <li>助力材料创新研发，加速科研成果转化</li>
          </ul>
        </el-card>
      </el-col>
      
      <el-col :md="12" :xs="24">
        <el-card shadow="hover">
          <h2>技术与支持</h2>
          <p>我们的平台基于先进的Web技术和数据科学方法构建，主要技术栈包括：</p>
          <div class="tech-stack">
            <div class="tech-item">
              <h4>前端技术</h4>
              <p>Vue.js, Element UI, ECharts, 3D可视化库</p>
            </div>
            <div class="tech-item">
              <h4>后端技术</h4>
              <p>Node.js, Express, MongoDB, Python科学计算生态系统</p>
            </div>
            <div class="tech-item">
              <h4>数据分析</h4>
              <p>数据挖掘算法、机器学习模型、材料信息学方法</p>
            </div>
            <div class="tech-item">
              <h4>基础设施</h4>
              <p>Docker容器化部署、自动化CI/CD流程</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="team-card" shadow="hover">
          <h2>团队介绍</h2>
          <p>我们的团队由材料科学、计算机科学和数据科学领域的专业人士组成，致力于为材料研究提供最优质的数据服务。</p>
          
          <div class="team-members">
            <div class="team-member">
              <el-avatar :size="80" src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"></el-avatar>
              <h4>张教授</h4>
              <p class="member-title">材料科学专家, 项目负责人</p>
              <p>专注于新能源材料研究，拥有20年材料科学研究经验</p>
            </div>
            
            <div class="team-member">
              <el-avatar :size="80" src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"></el-avatar>
              <h4>李博士</h4>
              <p class="member-title">数据科学专家</p>
              <p>材料信息学领域专家，负责数据挖掘和分析系统开发</p>
            </div>
            
            <div class="team-member">
              <el-avatar :size="80" src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"></el-avatar>
              <h4>王工程师</h4>
              <p class="member-title">全栈开发工程师</p>
              <p>负责平台架构设计和核心功能实现</p>
            </div>
            
            <div class="team-member">
              <el-avatar :size="80" src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"></el-avatar>
              <h4>陈博士</h4>
              <p class="member-title">材料数据库专家</p>
              <p>负责数据库构建和维护，专注材料数据标准化</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="contact-section">
      <el-col :span="24">
        <el-card shadow="hover">
          <h2>联系我们</h2>
          <div class="contact-info">
            <div class="contact-item">
              <i class="el-icon-location"></i>
              <span>地址：中国北京市海淀区学院路30号材料科学楼</span>
            </div>
            <div class="contact-item">
              <i class="el-icon-message"></i>
              <span>邮箱：<EMAIL></span>
            </div>
            <div class="contact-item">
              <i class="el-icon-phone"></i>
              <span>电话：+86-10-12345678</span>
            </div>
          </div>
          
          <div class="contact-form">
            <h3>留言反馈</h3>
            <el-form :model="contactForm" label-width="80px">
              <el-form-item label="姓名">
                <el-input v-model="contactForm.name" placeholder="请输入您的姓名"></el-input>
              </el-form-item>
              <el-form-item label="邮箱">
                <el-input v-model="contactForm.email" placeholder="请输入您的邮箱"></el-input>
              </el-form-item>
              <el-form-item label="留言内容">
                <el-input
                  type="textarea"
                  :rows="4"
                  v-model="contactForm.message"
                  placeholder="请输入留言内容"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="submitContact">提交留言</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'AboutView',
  data() {
    return {
      contactForm: {
        name: '',
        email: '',
        message: ''
      }
    }
  },
  methods: {
    submitContact() {
      this.$message({
        message: '感谢您的留言，我们将尽快回复！',
        type: 'success'
      })
      // 实际项目中这里会调用API提交留言
      this.contactForm = {
        name: '',
        email: '',
        message: ''
      }
    }
  }
}
</script>

<style scoped lang="scss">
.about-container {
  padding: 20px;
  
  .page-header {
    text-align: center;
    margin-bottom: 30px;
    
    h1 {
      font-size: 32px;
      font-weight: 600;
      margin-bottom: 10px;
    }
    
    p {
      font-size: 18px;
      color: #666;
    }
  }
  
  .introduction-card {
    margin-bottom: 30px;
    
    h2 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 20px;
      color: #303133;
    }
    
    p {
      font-size: 16px;
      line-height: 1.8;
      margin-bottom: 20px;
      color: #606266;
    }
    
    h3 {
      font-size: 20px;
      font-weight: 600;
      margin: 30px 0 20px;
      color: #303133;
    }
    
    .feature-list {
      .feature-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 25px;
        
        i {
          font-size: 30px;
          color: #409EFF;
          margin-right: 15px;
          padding-top: 5px;
        }
        
        .feature-content {
          flex: 1;
          
          h4 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #303133;
          }
          
          p {
            font-size: 14px;
            margin-bottom: 0;
            color: #606266;
          }
        }
      }
    }
  }
  
  .mission-section {
    margin-bottom: 30px;
    
    h2 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 20px;
      color: #303133;
    }
    
    p {
      font-size: 16px;
      line-height: 1.8;
      margin-bottom: 20px;
      color: #606266;
    }
    
    .mission-list {
      padding-left: 20px;
      margin-bottom: 20px;
      
      li {
        margin-bottom: 10px;
        font-size: 14px;
        color: #606266;
        line-height: 1.6;
      }
    }
    
    .tech-stack {
      .tech-item {
        margin-bottom: 15px;
        
        h4 {
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 5px;
          color: #303133;
        }
        
        p {
          font-size: 14px;
          margin-bottom: 5px;
          color: #606266;
        }
      }
    }
  }
  
  .team-card {
    margin-bottom: 30px;
    
    h2 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 20px;
      color: #303133;
    }
    
    p {
      font-size: 16px;
      line-height: 1.8;
      margin-bottom: 30px;
      color: #606266;
    }
    
    .team-members {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-around;
      
      .team-member {
        text-align: center;
        width: 200px;
        margin-bottom: 30px;
        
        h4 {
          margin: 15px 0 5px;
          font-size: 18px;
          font-weight: 600;
          color: #303133;
        }
        
        .member-title {
          font-size: 14px;
          color: #909399;
          margin-bottom: 10px;
          font-weight: normal;
        }
        
        p {
          font-size: 14px;
          line-height: 1.5;
          margin-bottom: 0;
        }
      }
    }
  }
  
  .contact-section {
    h2 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 20px;
      color: #303133;
    }
    
    .contact-info {
      margin-bottom: 30px;
      
      .contact-item {
        margin-bottom: 15px;
        font-size: 16px;
        color: #606266;
        
        i {
          font-size: 18px;
          color: #409EFF;
          margin-right: 10px;
        }
      }
    }
    
    .contact-form {
      h3 {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #303133;
      }
    }
  }
}

@media (max-width: 767px) {
  .team-members {
    justify-content: center;
    
    .team-member {
      width: 45% !important;
    }
  }
}
</style> 