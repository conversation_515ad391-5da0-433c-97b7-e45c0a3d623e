<template>
  <div class="search-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="search-header">
          <h1>高级搜索</h1>
          <p>精确定位所需材料数据和学习资源</p>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="6">
        <!-- 搜索筛选器 -->
        <el-card class="filter-card" shadow="never">
          <template #header>
            <div class="filter-header">
              <span>筛选条件</span>
              <el-button type="text" @click="resetFilters">重置筛选</el-button>
            </div>
          </template>
          
          <!-- 资源类型筛选 -->
          <div class="filter-group">
            <h3>资源类型</h3>
            <el-checkbox-group v-model="filters.resourceTypes">
              <el-checkbox label="structure">结构数据</el-checkbox>
              <el-checkbox label="document">文档资料</el-checkbox>
              <el-checkbox label="video">视频教程</el-checkbox>
              <el-checkbox label="question">问答内容</el-checkbox>
              <el-checkbox label="software">软件资源</el-checkbox>
            </el-checkbox-group>
          </div>
          
          <!-- 材料类型筛选 -->
          <div class="filter-group">
            <h3>材料类型</h3>
            <el-checkbox-group v-model="filters.materialTypes">
              <el-checkbox label="metal">金属材料</el-checkbox>
              <el-checkbox label="ceramic">陶瓷材料</el-checkbox>
              <el-checkbox label="polymer">高分子材料</el-checkbox>
              <el-checkbox label="composite">复合材料</el-checkbox>
              <el-checkbox label="semiconductor">半导体材料</el-checkbox>
            </el-checkbox-group>
          </div>
          
          <!-- 属性筛选 -->
          <div class="filter-group">
            <h3>物理性质</h3>
            
            <!-- 密度范围 -->
            <div class="range-filter">
              <span class="range-label">密度 (g/cm³)</span>
              <el-slider
                v-model="filters.density"
                range
                :min="0"
                :max="30"
                :step="0.1"
              ></el-slider>
              <div class="range-values">
                <span>{{ filters.density[0] }}</span>
                <span>{{ filters.density[1] }}</span>
              </div>
            </div>
            
            <!-- 带隙范围 -->
            <div class="range-filter">
              <span class="range-label">带隙 (eV)</span>
              <el-slider
                v-model="filters.bandGap"
                range
                :min="0"
                :max="10"
                :step="0.1"
              ></el-slider>
              <div class="range-values">
                <span>{{ filters.bandGap[0] }}</span>
                <span>{{ filters.bandGap[1] }}</span>
              </div>
            </div>
            
            <!-- 晶系选择 -->
            <div class="select-filter">
              <span class="select-label">晶系</span>
              <el-select v-model="filters.crystalSystem" placeholder="选择晶系" clearable style="width: 100%">
                <el-option label="立方晶系" value="cubic"></el-option>
                <el-option label="四方晶系" value="tetragonal"></el-option>
                <el-option label="正交晶系" value="orthorhombic"></el-option>
                <el-option label="六方晶系" value="hexagonal"></el-option>
                <el-option label="三方晶系" value="trigonal"></el-option>
                <el-option label="单斜晶系" value="monoclinic"></el-option>
                <el-option label="三斜晶系" value="triclinic"></el-option>
              </el-select>
            </div>
          </div>
          
          <!-- 元素组成筛选 -->
          <div class="filter-group">
            <h3>元素组成</h3>
            <div class="element-filter">
              <el-select
                v-model="filters.elements"
                multiple
                filterable
                placeholder="选择元素"
                style="width: 100%"
              >
                <el-option
                  v-for="element in periodicElements"
                  :key="element.symbol"
                  :label="`${element.symbol} - ${element.name}`"
                  :value="element.symbol"
                >
                  <span style="float: left">{{ element.symbol }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ element.name }}</span>
                </el-option>
              </el-select>
            </div>
            
            <div class="element-relation" v-if="filters.elements.length > 0">
              <el-radio-group v-model="filters.elementRelation">
                <el-radio label="all">包含所有</el-radio>
                <el-radio label="any">包含任一</el-radio>
                <el-radio label="only">仅包含这些</el-radio>
              </el-radio-group>
            </div>
          </div>
          
          <!-- 时间筛选 -->
          <div class="filter-group">
            <h3>发布时间</h3>
            <el-radio-group v-model="filters.timeRange">
              <el-radio label="all">全部时间</el-radio>
              <el-radio label="week">一周内</el-radio>
              <el-radio label="month">一个月内</el-radio>
              <el-radio label="year">一年内</el-radio>
              <el-radio label="custom">自定义</el-radio>
            </el-radio-group>
            
            <div v-if="filters.timeRange === 'custom'" class="date-range">
              <el-date-picker
                v-model="filters.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
              ></el-date-picker>
            </div>
          </div>
          
          <el-button type="primary" class="apply-filter-btn" @click="applyFilters">应用筛选</el-button>
        </el-card>
      </el-col>
      
      <el-col :span="18">
        <!-- 搜索区域 -->
        <el-card class="search-card" shadow="hover">
          <div class="search-box">
            <el-input
              v-model="searchQuery"
              placeholder="输入关键词、化学式、材料性质等"
              clearable
              class="search-input"
            >
              <template #append>
                <el-select v-model="searchMode" placeholder="搜索模式" style="width: 110px">
                  <el-option label="文本搜索" value="text"></el-option>
                  <el-option label="结构搜索" value="structure"></el-option>
                  <el-option label="性能搜索" value="property"></el-option>
                </el-select>
              </template>
            </el-input>
            <el-button type="primary" icon="el-icon-search" @click="search">搜索</el-button>
          </div>
          
          <div class="search-tabs">
            <el-tabs v-model="activeTab" @tab-click="handleTabClick">
              <el-tab-pane label="全部" name="all"></el-tab-pane>
              <el-tab-pane label="结构数据" name="structure"></el-tab-pane>
              <el-tab-pane label="文档资料" name="document"></el-tab-pane>
              <el-tab-pane label="问答" name="question"></el-tab-pane>
            </el-tabs>
          </div>
          
          <!-- 搜索模式说明 -->
          <div class="search-mode-help" v-if="searchMode === 'structure'">
            <el-alert
              title="结构搜索模式"
              type="info"
              description="您可以通过输入化学式(如SiO2)、结构文件上传或绘制结构来搜索相似的材料结构。"
              show-icon
              :closable="false"
            ></el-alert>
            <div class="upload-structure">
              <el-upload
                action="#"
                :auto-upload="false"
                :limit="1"
                accept=".cif,.xyz,.mol"
              >
                <el-button type="primary">上传结构文件</el-button>
                <template #tip>
                  <div class="el-upload__tip">支持CIF、XYZ、MOL等格式</div>
                </template>
              </el-upload>
              <el-button type="success">绘制结构</el-button>
            </div>
          </div>
          
          <div class="search-mode-help" v-if="searchMode === 'property'">
            <el-alert
              title="性能搜索模式"
              type="info"
              description="您可以通过指定所需的材料性能参数范围来查找符合条件的材料。"
              show-icon
              :closable="false"
            ></el-alert>
            <div class="property-inputs">
              <el-form :model="propertySearch" label-width="120px" size="small">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="带隙 (eV)">
                      <el-input-number v-model="propertySearch.bandGapMin" placeholder="最小值" :min="0" :step="0.1"></el-input-number>
                      <span class="range-separator">-</span>
                      <el-input-number v-model="propertySearch.bandGapMax" placeholder="最大值" :min="0" :step="0.1"></el-input-number>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="形成能 (eV/atom)">
                      <el-input-number v-model="propertySearch.formationEnergyMin" placeholder="最小值" :step="0.1"></el-input-number>
                      <span class="range-separator">-</span>
                      <el-input-number v-model="propertySearch.formationEnergyMax" placeholder="最大值" :step="0.1"></el-input-number>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="体积模量 (GPa)">
                      <el-input-number v-model="propertySearch.bulkModulusMin" placeholder="最小值" :min="0" :step="1"></el-input-number>
                      <span class="range-separator">-</span>
                      <el-input-number v-model="propertySearch.bulkModulusMax" placeholder="最大值" :min="0" :step="1"></el-input-number>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="导热系数 (W/mK)">
                      <el-input-number v-model="propertySearch.thermalConductivityMin" placeholder="最小值" :min="0" :step="0.1"></el-input-number>
                      <span class="range-separator">-</span>
                      <el-input-number v-model="propertySearch.thermalConductivityMax" placeholder="最大值" :min="0" :step="0.1"></el-input-number>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </div>
          
          <!-- 搜索结果 -->
          <div class="search-results" v-if="searchPerformed">
            <div class="results-header">
              <h2>搜索结果 ({{ filteredResults.length }})</h2>
              <div class="sort-options">
                <span>排序: </span>
                <el-radio-group v-model="sortOption" size="small">
                  <el-radio-button label="relevance">相关度</el-radio-button>
                  <el-radio-button label="date">发布日期</el-radio-button>
                  <el-radio-button label="downloads">下载量</el-radio-button>
                </el-radio-group>
              </div>
            </div>
            
            <div v-if="filteredResults.length > 0">
              <div v-for="result in paginatedResults" :key="result.id" class="result-item">
                <div class="result-type-tag">
                  <el-tag size="small" :type="getTagType(result.type)">{{ getTypeName(result.type) }}</el-tag>
                </div>
                <div class="result-content">
                  <h3 class="result-title">
                    <router-link :to="getDetailUrl(result)">{{ result.title }}</router-link>
                  </h3>
                  <div class="result-meta">
                    <span><i class="el-icon-user"></i> {{ result.author }}</span>
                    <span><i class="el-icon-date"></i> {{ formatDate(result.createdAt) }}</span>
                    <span><i class="el-icon-view"></i> {{ result.views }} 次查看</span>
                    <span v-if="result.downloads"><i class="el-icon-download"></i> {{ result.downloads }} 次下载</span>
                  </div>
                  <div class="result-description">{{ result.description }}</div>
                  <div class="result-tags" v-if="result.tags && result.tags.length">
                    <el-tag v-for="tag in result.tags" :key="tag" size="mini" effect="plain">{{ tag }}</el-tag>
                  </div>
                  
                  <!-- 如果是结构数据，显示额外信息 -->
                  <div class="structure-info" v-if="result.type === 'structure'">
                    <div class="structure-properties">
                      <span v-if="result.formula">{{ result.formula }}</span>
                      <span v-if="result.spaceGroup">{{ result.spaceGroup }}</span>
                      <span v-if="result.bandGap">带隙: {{ result.bandGap }} eV</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 分页 -->
              <div class="pagination-container">
                <el-pagination
                  background
                  layout="prev, pager, next"
                  :total="filteredResults.length"
                  :page-size="pageSize"
                  :current-page="currentPage"
                  @current-change="handlePageChange"
                ></el-pagination>
              </div>
            </div>
            
            <el-empty v-else description="没有找到符合条件的结果"></el-empty>
          </div>
          
          <!-- 初始状态或无结果 -->
          <div class="search-placeholder" v-if="!searchPerformed">
            <el-empty description="请输入搜索条件">
              <template #description>
                <p>输入关键词或选择搜索条件开始查找资源</p>
              </template>
            </el-empty>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'

export default {
  name: 'SearchView',
  setup() {
    // 搜索相关
    const searchQuery = ref('')
    const searchMode = ref('text')
    const searchPerformed = ref(false)
    const activeTab = ref('all')
    const sortOption = ref('relevance')
    
    // 分页相关
    const currentPage = ref(1)
    const pageSize = ref(10)
    
    // 筛选相关
    const filters = reactive({
      resourceTypes: [],
      materialTypes: [],
      density: [0, 30],
      bandGap: [0, 10],
      crystalSystem: '',
      elements: [],
      elementRelation: 'any',
      timeRange: 'all',
      dateRange: []
    })
    
    // 性能搜索相关
    const propertySearch = reactive({
      bandGapMin: null,
      bandGapMax: null,
      formationEnergyMin: null,
      formationEnergyMax: null,
      bulkModulusMin: null,
      bulkModulusMax: null,
      thermalConductivityMin: null,
      thermalConductivityMax: null
    })
    
    // 周期表元素数据
    const periodicElements = ref([
      { symbol: 'H', name: '氢' },
      { symbol: 'He', name: '氦' },
      { symbol: 'Li', name: '锂' },
      { symbol: 'Be', name: '铍' },
      { symbol: 'B', name: '硼' },
      { symbol: 'C', name: '碳' },
      { symbol: 'N', name: '氮' },
      { symbol: 'O', name: '氧' },
      { symbol: 'F', name: '氟' },
      { symbol: 'Ne', name: '氖' },
      { symbol: 'Na', name: '钠' },
      { symbol: 'Mg', name: '镁' },
      { symbol: 'Al', name: '铝' },
      { symbol: 'Si', name: '硅' },
      { symbol: 'P', name: '磷' },
      { symbol: 'S', name: '硫' },
      { symbol: 'Cl', name: '氯' },
      { symbol: 'Ar', name: '氩' },
      { symbol: 'K', name: '钾' },
      { symbol: 'Ca', name: '钙' },
      { symbol: 'Ti', name: '钛' },
      { symbol: 'Fe', name: '铁' },
      { symbol: 'Cu', name: '铜' },
      { symbol: 'Zn', name: '锌' },
      { symbol: 'Ag', name: '银' },
      { symbol: 'Au', name: '金' }
      // 这里只列出了部分元素，实际应用中应包含完整的元素表
    ])
    
    // 模拟搜索结果数据
    const searchResults = ref([
      {
        id: '1',
        title: 'SiO2晶体结构数据',
        type: 'structure',
        author: '材料研究所',
        createdAt: '2023-06-15T08:30:00Z',
        views: 1234,
        downloads: 456,
        description: '二氧化硅(SiO2)的晶体结构数据，包括原子坐标、晶格参数及相关物理性质。',
        tags: ['二氧化硅', '晶体结构', '热力学数据'],
        formula: 'SiO2',
        spaceGroup: 'P3221',
        bandGap: 5.7,
        density: 2.65
      },
      {
        id: '2',
        title: 'TiO2光催化性能研究',
        type: 'document',
        author: '光催化研究中心',
        createdAt: '2023-05-20T10:15:00Z',
        views: 876,
        downloads: 234,
        description: '详细分析了TiO2材料在不同晶型下的光催化性能差异，包括实验方法和性能测试结果。',
        tags: ['二氧化钛', '光催化', '性能测试'],
      },
      {
        id: '3',
        title: 'Materials Studio软件使用教程：晶体结构优化',
        type: 'video',
        author: '软件培训部',
        createdAt: '2023-04-12T14:25:00Z',
        views: 2345,
        downloads: 789,
        description: '详细讲解如何使用Materials Studio软件进行晶体结构优化，包括参数设置和结果分析。',
        tags: ['Materials Studio', '教程', '结构优化'],
      },
      {
        id: '4',
        title: '如何在Materials Studio中计算能带结构？',
        type: 'question',
        author: '研究生小王',
        createdAt: '2023-03-05T09:40:00Z',
        views: 567,
        description: '我在使用MS计算Si能带结构时遇到了收敛问题，请问如何设置合适的参数？',
        tags: ['能带计算', '收敛问题', '参数设置'],
      },
      {
        id: '5',
        title: 'Fe-Cr合金相图计算与分析',
        type: 'document',
        author: '计算材料学实验室',
        createdAt: '2023-02-18T16:50:00Z',
        views: 890,
        downloads: 345,
        description: '使用CALPHAD方法计算Fe-Cr二元合金相图，并与实验数据进行对比分析。',
        tags: ['相图', 'CALPHAD', '合金'],
      },
      {
        id: '6',
        title: 'GaN半导体材料结构数据',
        type: 'structure',
        author: '半导体材料中心',
        createdAt: '2023-01-10T11:20:00Z',
        views: 1456,
        downloads: 678,
        description: '氮化镓(GaN)的晶体结构数据，包括原子坐标、晶格参数及电子性质。',
        tags: ['氮化镓', '半导体', '晶体结构'],
        formula: 'GaN',
        spaceGroup: 'P63mc',
        bandGap: 3.4,
        density: 6.15
      }
    ])
    
    // 筛选后的结果
    const filteredResults = computed(() => {
      let results = [...searchResults.value]
      
      // 根据选项卡筛选
      if (activeTab.value !== 'all') {
        results = results.filter(item => item.type === activeTab.value)
      }
      
      // 根据资源类型筛选
      if (filters.resourceTypes.length > 0) {
        results = results.filter(item => filters.resourceTypes.includes(item.type))
      }
      
      // 根据排序选项排序
      if (sortOption.value === 'date') {
        results.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      } else if (sortOption.value === 'downloads') {
        results.sort((a, b) => (b.downloads || 0) - (a.downloads || 0))
      }
      
      return results
    })
    
    // 分页后的结果
    const paginatedResults = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return filteredResults.value.slice(start, end)
    })
    
    // 方法
    const search = () => {
      searchPerformed.value = true
      currentPage.value = 1
      console.log(`执行搜索，关键词: ${searchQuery.value}, 模式: ${searchMode.value}`)
      // 实际项目中这里会调用API进行搜索
    }
    
    const handleTabClick = (tab) => {
      console.log(`切换到标签: ${tab.name}`)
      // 实际项目中可能需要重新搜索或过滤结果
    }
    
    const resetFilters = () => {
      Object.assign(filters, {
        resourceTypes: [],
        materialTypes: [],
        density: [0, 30],
        bandGap: [0, 10],
        crystalSystem: '',
        elements: [],
        elementRelation: 'any',
        timeRange: 'all',
        dateRange: []
      })
    }
    
    const applyFilters = () => {
      console.log('应用筛选条件:', filters)
      currentPage.value = 1
      // 实际项目中这里会根据筛选条件重新请求数据
    }
    
    const handlePageChange = (page) => {
      currentPage.value = page
    }
    
    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    }
    
    const getTagType = (type) => {
      const types = {
        structure: '',
        document: 'success',
        video: 'warning',
        question: 'info',
        software: 'danger'
      }
      return types[type] || ''
    }
    
    const getTypeName = (type) => {
      const names = {
        structure: '结构数据',
        document: '文档资料',
        video: '视频教程',
        question: '问答',
        software: '软件资源'
      }
      return names[type] || type
    }
    
    const getDetailUrl = (result) => {
      const urls = {
        structure: `/resources/${result.id}`,
        document: `/documents/${result.id}`,
        video: `/videos/${result.id}`,
        question: `/questions/${result.id}`,
        software: `/software/${result.id}`
      }
      return urls[result.type] || '/'
    }
    
    onMounted(() => {
      // 可能需要加载一些初始数据
      console.log('高级搜索页面已加载')
    })
    
    return {
      // 状态
      searchQuery,
      searchMode,
      searchPerformed,
      activeTab,
      sortOption,
      currentPage,
      pageSize,
      filters,
      propertySearch,
      periodicElements,
      filteredResults,
      paginatedResults,
      
      // 方法
      search,
      handleTabClick,
      resetFilters,
      applyFilters,
      handlePageChange,
      formatDate,
      getTagType,
      getTypeName,
      getDetailUrl
    }
  }
}
</script>

<style scoped lang="scss">
.search-container {
  padding: 20px;
  
  .search-header {
    text-align: center;
    margin-bottom: 30px;
    
    h1 {
      font-size: 28px;
      font-weight: 600;
      margin-bottom: 10px;
    }
    
    p {
      color: #666;
      font-size: 16px;
    }
  }
  
  .filter-card {
    margin-bottom: 20px;
    
    .filter-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .filter-group {
      margin-bottom: 20px;
      
      h3 {
        font-size: 16px;
        margin-bottom: 15px;
        font-weight: 600;
        color: #333;
      }
      
      .el-checkbox-group {
        display: flex;
        flex-direction: column;
        
        .el-checkbox {
          margin-left: 0;
          margin-bottom: 8px;
        }
      }
      
      .el-radio-group {
        display: flex;
        flex-direction: column;
        
        .el-radio {
          margin-left: 0;
          margin-bottom: 8px;
        }
      }
      
      .range-filter {
        margin-bottom: 15px;
        
        .range-label {
          display: block;
          margin-bottom: 8px;
          font-size: 14px;
          color: #666;
        }
        
        .range-values {
          display: flex;
          justify-content: space-between;
          font-size: 12px;
          color: #999;
          margin-top: 5px;
        }
      }
      
      .select-filter {
        margin-bottom: 15px;
        
        .select-label {
          display: block;
          margin-bottom: 8px;
          font-size: 14px;
          color: #666;
        }
      }
      
      .element-filter {
        margin-bottom: 10px;
      }
      
      .element-relation {
        margin-top: 10px;
      }
      
      .date-range {
        margin-top: 10px;
      }
    }
    
    .apply-filter-btn {
      width: 100%;
    }
  }
  
  .search-card {
    .search-box {
      display: flex;
      margin-bottom: 20px;
      
      .search-input {
        flex-grow: 1;
        margin-right: 10px;
      }
    }
    
    .search-tabs {
      margin-bottom: 20px;
    }
    
    .search-mode-help {
      margin-bottom: 20px;
      
      .upload-structure {
        margin-top: 15px;
        display: flex;
        align-items: center;
        
        .el-upload {
          margin-right: 15px;
        }
      }
      
      .property-inputs {
        margin-top: 15px;
        
        .range-separator {
          margin: 0 5px;
          color: #999;
        }
      }
    }
    
    .search-results {
      .results-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        
        h2 {
          font-size: 18px;
          font-weight: 600;
          margin: 0;
        }
        
        .sort-options {
          display: flex;
          align-items: center;
          
          span {
            margin-right: 10px;
            color: #666;
          }
        }
      }
      
      .result-item {
        padding: 15px 0;
        border-bottom: 1px solid #eee;
        display: flex;
        
        &:last-child {
          border-bottom: none;
        }
        
        .result-type-tag {
          padding-right: 15px;
          display: flex;
          align-items: flex-start;
        }
        
        .result-content {
          flex-grow: 1;
          
          .result-title {
            margin: 0 0 10px 0;
            font-size: 18px;
            
            a {
              color: #409EFF;
              text-decoration: none;
              
              &:hover {
                text-decoration: underline;
              }
            }
          }
          
          .result-meta {
            margin-bottom: 10px;
            color: #666;
            font-size: 13px;
            
            span {
              margin-right: 15px;
              
              i {
                margin-right: 5px;
              }
            }
          }
          
          .result-description {
            margin-bottom: 10px;
            font-size: 14px;
            color: #333;
            line-height: 1.5;
          }
          
          .result-tags {
            margin-bottom: 10px;
            
            .el-tag {
              margin-right: 8px;
              margin-bottom: 5px;
            }
          }
          
          .structure-info {
            background-color: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            
            .structure-properties {
              display: flex;
              flex-wrap: wrap;
              
              span {
                margin-right: 15px;
                font-size: 13px;
                color: #555;
              }
            }
          }
        }
      }
      
      .pagination-container {
        margin-top: 30px;
        text-align: center;
      }
    }
    
    .search-placeholder {
      margin: 50px 0;
    }
  }
}
</style> 