import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import api from '@/api'
import { getToken, setToken, removeToken } from '@/utils/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const token = ref(getToken())
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isMember = computed(() => user.value?.isMember || false)
  const isAdmin = computed(() => user.value?.isAdmin || false)
  const userInfo = computed(() => user.value || {})

  // 方法
  const login = async (credentials) => {
    try {
      isLoading.value = true
      const response = await api.auth.login(credentials)
      
      if (response.success) {
        const { accessToken, refreshToken, ...userData } = response.data
        
        // 保存令牌
        setToken(accessToken)
        token.value = accessToken
        
        // 保存用户信息
        user.value = userData
        
        // 保存刷新令牌到localStorage
        localStorage.setItem('refreshToken', refreshToken)
        
        ElMessage.success('登录成功')
        return response
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      ElMessage.error(error.message || '登录失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const register = async (userData) => {
    try {
      isLoading.value = true
      const response = await api.auth.register(userData)
      
      if (response.success) {
        const { accessToken, refreshToken, ...userInfo } = response.data
        
        // 保存令牌
        setToken(accessToken)
        token.value = accessToken
        
        // 保存用户信息
        user.value = userInfo
        
        // 保存刷新令牌
        localStorage.setItem('refreshToken', refreshToken)
        
        ElMessage.success('注册成功')
        return response
      } else {
        throw new Error(response.message || '注册失败')
      }
    } catch (error) {
      ElMessage.error(error.message || '注册失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    try {
      if (token.value) {
        await api.auth.logout()
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地数据
      removeToken()
      token.value = null
      user.value = null
      localStorage.removeItem('refreshToken')
      
      ElMessage.success('已退出登录')
    }
  }

  const refreshToken = async () => {
    try {
      const refreshTokenValue = localStorage.getItem('refreshToken')
      if (!refreshTokenValue) {
        throw new Error('没有刷新令牌')
      }

      const response = await api.auth.refresh({ refreshToken: refreshTokenValue })
      
      if (response.success) {
        const { accessToken, refreshToken: newRefreshToken } = response.data
        
        setToken(accessToken)
        token.value = accessToken
        localStorage.setItem('refreshToken', newRefreshToken)
        
        return accessToken
      } else {
        throw new Error('刷新令牌失败')
      }
    } catch (error) {
      // 刷新失败，清除所有认证信息
      await logout()
      throw error
    }
  }

  const fetchUserInfo = async () => {
    try {
      if (!token.value) {
        return null
      }

      const response = await api.auth.me()
      
      if (response.success) {
        user.value = response.data
        return response.data
      } else {
        throw new Error('获取用户信息失败')
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，可能是令牌过期
      await logout()
      throw error
    }
  }

  const updateUserInfo = (newUserInfo) => {
    if (user.value) {
      user.value = { ...user.value, ...newUserInfo }
    }
  }

  const initializeAuth = async () => {
    try {
      if (token.value) {
        // 尝试获取用户信息
        await fetchUserInfo()
      }
    } catch (error) {
      console.error('初始化认证状态失败:', error)
      // 初始化失败，清除认证信息
      await logout()
    }
  }

  const checkAuthStatus = () => {
    return !!token.value && !!user.value
  }

  return {
    // 状态
    user: readonly(user),
    token: readonly(token),
    isLoading: readonly(isLoading),
    
    // 计算属性
    isAuthenticated,
    isMember,
    isAdmin,
    userInfo,
    
    // 方法
    login,
    register,
    logout,
    refreshToken,
    fetchUserInfo,
    updateUserInfo,
    initializeAuth,
    checkAuthStatus
  }
})
