<template>
  <div class="databases-container">
    <div class="page-header">
      <h1>材料数据资源中心</h1>
      <p>探索丰富的材料科学数据库，助力您的研究与创新</p>
    </div>

    <!-- 数据库类别展示 -->
    <el-row :gutter="20" class="database-categories">
      <el-col :xs="24" :md="8" v-for="(category, index) in databaseCategories" :key="index">
        <el-card shadow="hover" class="category-card" @click.native="navigateToCategory(category.type)">
          <div class="category-icon">
            <i :class="category.icon"></i>
          </div>
          <h2>{{ category.title }}</h2>
          <p>{{ category.description }}</p>
          <el-button type="primary" plain>浏览数据库</el-button>
          <div class="database-count">
            <span>{{ category.count }}个数据库</span>
            <span>{{ category.entriesCount }}条数据</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 热门数据库 -->
    <div class="section-header">
      <h2>热门数据库</h2>
      <el-button type="text" @click="viewAllDatabases">查看全部</el-button>
    </div>

    <el-row :gutter="20" class="popular-databases">
      <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="(db, index) in popularDatabases" :key="index">
        <el-card shadow="hover" class="database-card">
          <div class="db-header">
            <img :src="db.logo" alt="数据库logo" class="db-logo" />
            <div class="db-title">
              <h3>{{ db.name }}</h3>
              <div class="db-meta">
                <el-tag size="mini" :type="getTagType(db.category)">{{ db.category }}</el-tag>
                <span class="data-count">{{ db.entriesCount }}条数据</span>
              </div>
            </div>
          </div>
          <p class="db-description">{{ db.description }}</p>
          <div class="db-features">
            <div class="feature-item" v-for="(feature, i) in db.features" :key="i">
              <i class="el-icon-check"></i>
              <span>{{ feature }}</span>
            </div>
          </div>
          <div class="db-actions">
            <el-button type="primary" size="small" @click="navigateToDatabase(db.id)">访问数据库</el-button>
            <el-button type="text" size="small" @click="showDatabaseInfo(db)">详细介绍</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最新数据 -->
    <div class="section-header">
      <h2>最新数据</h2>
      <el-button type="text" @click="viewAllLatestData">查看全部</el-button>
    </div>

    <el-table
      :data="latestData"
      style="width: 100%"
      :stripe="true"
      :border="true"
      class="latest-data-table"
    >
      <el-table-column prop="formula" label="化学式" width="120"></el-table-column>
      <el-table-column prop="name" label="材料名称"></el-table-column>
      <el-table-column prop="category" label="分类" width="120">
        <template #default="scope">
          <el-tag size="mini" :type="getTagType(scope.row.category)">{{ scope.row.category }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="source" label="数据来源" width="180"></el-table-column>
      <el-table-column prop="date" label="更新日期" width="120"></el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="scope">
          <el-button type="text" size="small" @click="viewMaterialDetail(scope.row)">查看详情</el-button>
          <el-button type="text" size="small" @click="downloadMaterialData(scope.row)">下载</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 数据库统计 -->
    <el-row :gutter="20" class="database-statistics">
      <el-col :span="24">
        <el-card shadow="hover" class="statistics-card">
          <template #header>
            <div class="card-header">
              <span>数据库统计</span>
              <el-button type="text" @click="refreshStats">更新统计</el-button>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :xs="12" :sm="6" :md="3" v-for="(stat, index) in databaseStats" :key="index">
              <div class="stat-item">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据库详情对话框 -->
    <el-dialog
      :title="selectedDatabase.name"
      :visible.sync="databaseInfoVisible"
      width="60%"
      class="database-info-dialog"
    >
      <div class="database-detail" v-if="selectedDatabase">
        <div class="database-detail-header">
          <img :src="selectedDatabase.logo" alt="数据库logo" class="detail-logo" />
          <div class="detail-meta">
            <el-tag size="small" :type="getTagType(selectedDatabase.category)">{{ selectedDatabase.category }}</el-tag>
            <span>数据量: {{ selectedDatabase.entriesCount }}</span>
            <span>更新时间: {{ selectedDatabase.lastUpdate }}</span>
          </div>
        </div>
        <p class="detail-description">{{ selectedDatabase.fullDescription || selectedDatabase.description }}</p>
        
        <div class="detail-section">
          <h4>主要功能</h4>
          <ul class="feature-list">
            <li v-for="(feature, index) in selectedDatabase.features" :key="index">
              <i class="el-icon-check"></i>
              <span>{{ feature }}</span>
            </li>
          </ul>
        </div>
        
        <div class="detail-section">
          <h4>数据类型</h4>
          <div class="data-types">
            <el-tag v-for="(type, index) in selectedDatabase.dataTypes" :key="index" size="small" effect="plain" class="data-type-tag">
              {{ type }}
            </el-tag>
          </div>
        </div>
        
        <div class="detail-section" v-if="selectedDatabase.screenshots && selectedDatabase.screenshots.length">
          <h4>界面预览</h4>
          <el-carousel :interval="4000" type="card" height="200px">
            <el-carousel-item v-for="(screenshot, index) in selectedDatabase.screenshots" :key="index">
              <img :src="screenshot" alt="数据库截图" class="screenshot-img" />
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="databaseInfoVisible = false">关闭</el-button>
        <el-button type="primary" @click="navigateToDatabase(selectedDatabase.id)">访问数据库</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import api from '@/api'

export default {
  name: 'DatabasesView',
  mounted() {
    this.fetchDatabases()
  },
  data() {
    return {
      databaseCategories: [
        {
          title: '结构数据库',
          description: '涵盖无机材料、金属、有机材料等晶体结构数据',
          icon: 'el-icon-data-analysis',
          type: 'structure',
          count: 12,
          entriesCount: '1,250,000+'
        },
        {
          title: '性能数据库',
          description: '提供材料电学、磁学、热学、力学等性能数据',
          icon: 'el-icon-data-board',
          type: 'property',
          count: 8,
          entriesCount: '820,000+'
        },
        {
          title: '工业材料库',
          description: '收录商用材料产品、标准规范及应用数据',
          icon: 'el-icon-s-operation',
          type: 'industrial',
          count: 5,
          entriesCount: '650,000+'
        }
      ],
      popularDatabases: [
        {
          id: 1,
          name: '晶体结构数据库',
          logo: 'https://via.placeholder.com/80x40?text=CSD',
          category: '结构数据库',
          description: '全球最大的无机晶体结构数据库，包含超过35万种无机材料的结构数据',
          entriesCount: '350,000+',
          features: ['原子坐标数据', '晶格参数', '空间群信息', 'CIF文件下载'],
          lastUpdate: '2023-06-15',
          fullDescription: '晶体结构数据库是当前全球最大的无机晶体材料结构数据库，收录了超过35万种无机材料的精确晶体结构数据。数据来源包括实验测定和理论计算的结构，覆盖了从单质到复杂氧化物的广泛材料体系。每个结构条目包含完整的晶格参数、原子坐标、空间群信息及相关的实验或计算条件。该数据库支持多种搜索方式，包括化学成分、空间群、晶格参数范围等，并提供结构可视化和CIF文件下载功能。',
          dataTypes: ['晶体结构', '晶格参数', '空间群', '原子坐标', 'Wyckoff位置', '价态信息'],
          screenshots: [
            'https://via.placeholder.com/800x450?text=Screenshot1',
            'https://via.placeholder.com/800x450?text=Screenshot2'
          ]
        },
        {
          id: 2,
          name: '材料性能数据库',
          logo: 'https://via.placeholder.com/80x40?text=MPD',
          category: '性能数据库',
          description: '提供超过15万种材料的电子、力学、热学等性能数据，支持性能对比和预测',
          entriesCount: '150,000+',
          features: ['多物理场性能', '温度相关性能', '性能对比分析', '数据可视化'],
          lastUpdate: '2023-05-20',
          fullDescription: '材料性能数据库提供了超过15万种材料的综合性能数据，涵盖电子、力学、热学、光学等多个领域。数据来源包括高质量的实验测量和高精度第一性原理计算结果。该数据库的特色在于提供不同温度、压力条件下的材料性能数据，支持性能随环境条件变化的趋势分析。用户可以方便地进行多材料、多性能的对比分析，并通过直观的数据可视化工具生成高质量图表。此外，该数据库还提供基于机器学习的材料性能预测功能。',
          dataTypes: ['电子性能', '力学性能', '热学性能', '光学性能', '磁学性能', '输运性能'],
          screenshots: [
            'https://via.placeholder.com/800x450?text=Screenshot1',
            'https://via.placeholder.com/800x450?text=Screenshot2'
          ]
        },
        {
          id: 3,
          name: '相图数据库',
          logo: 'https://via.placeholder.com/80x40?text=PDB',
          category: '热力学数据库',
          description: '收录超过10,000个材料体系的相图数据，覆盖二元、三元及多元合金系统',
          entriesCount: '10,000+',
          features: ['二元相图', '三元相图', '相平衡数据', '热力学参数'],
          lastUpdate: '2023-04-10',
          fullDescription: '相图数据库是材料科学领域最全面的相平衡数据库之一，收录了超过10,000个材料体系的相图数据。数据库覆盖了二元、三元及部分多元合金系统，提供实验测定的相图以及基于CALPHAD方法计算的相平衡数据。每个相图条目包含完整的相区信息、相变温度、共晶/共析点位置等关键数据。该数据库还提供与相图相关的热力学参数，如吉布斯自由能、焓、熵等，支持研究者进行热力学分析和预测。用户可以通过交互式界面调整成分、温度条件，生成定制化的相图。',
          dataTypes: ['相图', '相平衡数据', '热力学参数', '相变温度', '固溶度限'],
          screenshots: [
            'https://via.placeholder.com/800x450?text=Screenshot1',
            'https://via.placeholder.com/800x450?text=Screenshot2'
          ]
        },
        {
          id: 4,
          name: '工业合金数据库',
          logo: 'https://via.placeholder.com/80x40?text=IAD',
          category: '工业材料库',
          description: '提供全球24万+金属材料数据，包含各国标准、厂商信息及完整性能参数',
          entriesCount: '240,000+',
          features: ['全球标准覆盖', '供应商信息', '加工工艺数据', '应用案例'],
          lastUpdate: '2023-03-15',
          fullDescription: '工业合金数据库是面向工程应用的专业数据库，提供全球24万余种金属材料的详细数据。数据库收录了全球主要国家和地区的材料标准，包括ASTM、ISO、JIS、GB等，并提供标准间的对照关系。每种合金材料都包含详细的化学成分、物理性能、机械性能、热处理工艺参数等信息，部分材料还提供腐蚀性能、焊接性能、加工性能等专业数据。该数据库的特色在于提供材料供应商信息、价格趋势、应用案例等实用信息，帮助用户进行材料选型和采购决策。',
          dataTypes: ['化学成分', '物理性能', '机械性能', '热处理工艺', '标准规范', '供应商信息'],
          screenshots: [
            'https://via.placeholder.com/800x450?text=Screenshot1',
            'https://via.placeholder.com/800x450?text=Screenshot2'
          ]
        }
      ],
      latestData: [
        { id: 1, formula: 'Ti3C2', name: 'MXene钛碳化物', category: '二维材料', source: '计算材料数据库', date: '2023-06-25' },
        { id: 2, formula: 'CH3NH3PbI3', name: '甲胺铅碘钙钛矿', category: '光电材料', source: '材料性能数据库', date: '2023-06-22' },
        { id: 3, formula: 'Li1.2Ni0.2Mn0.6O2', name: '富锂锰基正极材料', category: '电池材料', source: '工业材料数据库', date: '2023-06-20' },
        { id: 4, formula: 'CsPbBr3', name: '铯铅溴钙钛矿', category: '光电材料', source: '晶体结构数据库', date: '2023-06-18' },
        { id: 5, formula: 'Fe3Al', name: '铁铝金属间化合物', category: '金属间化合物', source: '相图数据库', date: '2023-06-15' },
        { id: 6, formula: 'SiC', name: '碳化硅', category: '宽禁带半导体', source: '半导体材料数据库', date: '2023-06-12' }
      ],
      databaseStats: [
        { label: '数据库总数', value: '25+' },
        { label: '材料条目', value: '2.7M+' },
        { label: '属性数据', value: '15M+' },
        { label: '文献引用', value: '320K+' },
        { label: '月活用户', value: '35K+' },
        { label: '数据下载', value: '5M+' },
        { label: '更新频率', value: '每日' },
        { label: '合作机构', value: '120+' }
      ],
      databaseInfoVisible: false,
      selectedDatabase: {}
    }
  },
  methods: {
    async fetchDatabases() {
      try {
        const response = await api.databases.getAll()
        if (response.success && response.data) {
          console.log('获取到数据库数据:', response.data)
          // 更新热门数据库
          this.popularDatabases = response.data.map(db => ({
            id: db.id,
            name: db.name,
            category: db.category,
            description: db.description,
            logo: db.logo || '/images/database-placeholder.png',
            entriesCount: this.formatNumber(db.entryCount),
            features: db.dataTypes || [],
            lastUpdate: db.lastUpdate
          }))
        }
      } catch (error) {
        console.error('获取数据库失败:', error)
        this.$message.error('获取数据库信息失败，请稍后再试')
      }
    },
    
    formatNumber(num) {
      return num ? num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') : '0'
    },
    
    navigateToCategory(type) {
      this.$router.push(`/databases/${type}`)
    },
    
    viewAllDatabases() {
      this.$router.push('/search?type=database')
    },
    
    navigateToDatabase(id) {
      this.databaseInfoVisible = false
      this.$router.push(`/databases/${id}`)
    },
    
    showDatabaseInfo(db) {
      this.selectedDatabase = db
      this.databaseInfoVisible = true
    },
    
    viewAllLatestData() {
      this.$router.push('/search?sort=date&order=desc')
    },
    
    viewMaterialDetail(material) {
      this.$router.push(`/materials/${material.id}`)
    },
    
    downloadMaterialData(material) {
      this.$message({
        message: `开始下载 ${material.formula} (${material.name}) 的数据`,
        type: 'success'
      })
      // 实际项目中这里会调用下载API
    },
    
    refreshStats() {
      this.$message({
        message: '数据库统计已更新',
        type: 'success'
      })
      // 实际项目中这里会请求最新的统计数据
    },
    
    getTagType(category) {
      const types = {
        '结构数据库': '',
        '性能数据库': 'success',
        '热力学数据库': 'warning',
        '工业材料库': 'info',
        '二维材料': '',
        '光电材料': 'success',
        '电池材料': 'warning',
        '金属间化合物': 'info',
        '宽禁带半导体': 'danger'
      }
      return types[category] || ''
    }
  }
}
</script>

<style scoped lang="scss">
.databases-container {
  padding: 20px;
  
  .page-header {
    text-align: center;
    margin-bottom: 40px;
    
    h1 {
      font-size: 32px;
      font-weight: 600;
      margin-bottom: 15px;
      background-image: linear-gradient(to right, #1890ff, #41d0cd);
      -webkit-background-clip: text;
      color: transparent;
    }
    
    p {
      font-size: 18px;
      color: #666;
      max-width: 800px;
      margin: 0 auto;
    }
  }
  
  .database-categories {
    margin-bottom: 50px;
    
    .category-card {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      padding: 30px 20px;
      transition: all 0.3s;
      cursor: pointer;
      
      &:hover {
        transform: translateY(-10px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      }
      
      .category-icon {
        font-size: 60px;
        color: #409EFF;
        margin-bottom: 20px;
      }
      
      h2 {
        font-size: 24px;
        margin-bottom: 15px;
        font-weight: 600;
      }
      
      p {
        font-size: 16px;
        color: #666;
        margin-bottom: 20px;
        flex-grow: 1;
      }
      
      .database-count {
        margin-top: 15px;
        font-size: 14px;
        color: #999;
        
        span {
          margin: 0 10px;
        }
      }
    }
  }
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    
    h2 {
      font-size: 22px;
      font-weight: 600;
      margin: 0;
    }
  }
  
  .popular-databases {
    margin-bottom: 50px;
    
    .database-card {
      height: 100%;
      margin-bottom: 20px;
      transition: all 0.3s;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }
      
      .db-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        
        .db-logo {
          width: 80px;
          height: 40px;
          object-fit: contain;
          margin-right: 15px;
        }
        
        .db-title {
          flex-grow: 1;
          
          h3 {
            margin: 0 0 5px 0;
            font-size: 18px;
            font-weight: 600;
          }
          
          .db-meta {
            display: flex;
            align-items: center;
            font-size: 12px;
            
            .data-count {
              margin-left: 10px;
              color: #999;
            }
          }
        }
      }
      
      .db-description {
        font-size: 14px;
        color: #666;
        margin-bottom: 15px;
        min-height: 60px;
      }
      
      .db-features {
        margin-bottom: 15px;
        
        .feature-item {
          margin-bottom: 8px;
          font-size: 13px;
          color: #555;
          
          i {
            color: #67C23A;
            margin-right: 5px;
          }
        }
      }
      
      .db-actions {
        display: flex;
        justify-content: space-between;
      }
    }
  }
  
  .latest-data-table {
    margin-bottom: 50px;
  }
  
  .database-statistics {
    .statistics-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .stat-item {
        text-align: center;
        padding: 15px 0;
        
        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: #409EFF;
          margin-bottom: 5px;
        }
        
        .stat-label {
          font-size: 14px;
          color: #666;
        }
      }
    }
  }
}

.database-info-dialog {
  .database-detail {
    .database-detail-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      
      .detail-logo {
        width: 120px;
        height: 60px;
        object-fit: contain;
        margin-right: 20px;
      }
      
      .detail-meta {
        display: flex;
        flex-direction: column;
        
        span {
          margin-top: 5px;
          font-size: 14px;
          color: #666;
        }
      }
    }
    
    .detail-description {
      font-size: 15px;
      line-height: 1.6;
      margin-bottom: 20px;
      color: #333;
    }
    
    .detail-section {
      margin-bottom: 20px;
      
      h4 {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 10px;
        color: #333;
      }
      
      .feature-list {
        padding-left: 20px;
        
        li {
          margin-bottom: 8px;
          font-size: 14px;
          
          i {
            color: #67C23A;
            margin-right: 5px;
          }
        }
      }
      
      .data-types {
        display: flex;
        flex-wrap: wrap;
        
        .data-type-tag {
          margin-right: 10px;
          margin-bottom: 10px;
        }
      }
    }
    
    .screenshot-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}
</style> 