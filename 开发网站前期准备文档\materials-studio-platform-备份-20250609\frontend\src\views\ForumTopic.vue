<template>
  <div class="forum-topic-container">
    <div class="back-button" @click="goBack">
      <i class="el-icon-arrow-left"></i> 返回论坛列表
    </div>
    
    <el-card class="topic-card" shadow="hover">
      <div class="topic-header">
        <div class="topic-title">
          <h1>{{ topic.title }}</h1>
          <div class="topic-meta">
            <el-tag 
              size="small" 
              :type="getCategoryTagType(topic.category)"
              effect="plain"
            >
              {{ getCategoryName(topic.category) }}
            </el-tag>
            <span class="topic-time">{{ formatDate(topic.createdAt) }}</span>
          </div>
        </div>
      </div>
      
      <div class="topic-content">
        <div class="author-info">
          <el-avatar :size="50" :src="topic.author.avatar"></el-avatar>
          <div class="author-details">
            <div class="author-name">{{ topic.author.name }}</div>
            <div class="author-title">{{ topic.author.title }}</div>
          </div>
        </div>
        
        <div class="content-body">
          <div v-html="topic.content"></div>
          
          <div class="content-tags" v-if="topic.tags && topic.tags.length > 0">
            <el-tag
              v-for="tag in topic.tags"
              :key="tag"
              size="small"
              effect="plain"
              class="tag-item"
            >{{ tag }}</el-tag>
          </div>
          
          <div class="content-attachments" v-if="topic.attachments && topic.attachments.length > 0">
            <h3>附件</h3>
            <div class="attachment-list">
              <div 
                v-for="attachment in topic.attachments"
                :key="attachment.id"
                class="attachment-item"
                @click="downloadAttachment(attachment)"
              >
                <i :class="getAttachmentIcon(attachment.type)"></i>
                <span class="attachment-name">{{ attachment.name }}</span>
                <span class="attachment-size">({{ formatSize(attachment.size) }})</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="topic-actions">
        <div class="action-buttons">
          <el-button 
            size="small" 
            :type="topic.isLiked ? 'primary' : ''" 
            icon="el-icon-star-off"
            @click="toggleLike"
          >
            {{ topic.likes }} 赞
          </el-button>
          <el-button 
            size="small" 
            icon="el-icon-collection"
            @click="collectTopic"
          >
            收藏
          </el-button>
          <el-button 
            size="small" 
            icon="el-icon-share"
            @click="shareTopic"
          >
            分享
          </el-button>
        </div>
        
        <div class="topic-stats">
          <span class="stat-item">
            <i class="el-icon-view"></i> {{ topic.views }} 阅读
          </span>
          <span class="stat-item">
            <i class="el-icon-chat-dot-square"></i> {{ replies.length }} 回复
          </span>
        </div>
      </div>
    </el-card>
    
    <div class="replies-section">
      <h2 class="section-title">{{ replies.length }} 条回复</h2>
      
      <div class="replies-filter">
        <el-radio-group v-model="replySort" size="small" @change="sortReplies">
          <el-radio-button label="earliest">最早</el-radio-button>
          <el-radio-button label="latest">最新</el-radio-button>
          <el-radio-button label="popular">热门</el-radio-button>
        </el-radio-group>
      </div>
      
      <el-card 
        v-for="(reply, index) in sortedReplies"
        :key="reply.id"
        class="reply-card"
        shadow="hover"
      >
        <div class="reply-header">
          <div class="reply-info">
            <el-avatar :size="40" :src="reply.author.avatar"></el-avatar>
            <div class="reply-author">
              <div class="author-name">{{ reply.author.name }}</div>
              <div class="author-title">{{ reply.author.title }}</div>
            </div>
          </div>
          <div class="reply-index">#{{ index + 1 }}</div>
        </div>
        
        <div class="reply-content">
          <div v-html="reply.content"></div>
          
          <div class="reply-attachments" v-if="reply.attachments && reply.attachments.length > 0">
            <div class="attachment-list">
              <div 
                v-for="attachment in reply.attachments"
                :key="attachment.id"
                class="attachment-item"
                @click="downloadAttachment(attachment)"
              >
                <i :class="getAttachmentIcon(attachment.type)"></i>
                <span class="attachment-name">{{ attachment.name }}</span>
                <span class="attachment-size">({{ formatSize(attachment.size) }})</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="reply-footer">
          <div class="reply-time">{{ formatDate(reply.createdAt) }}</div>
          <div class="reply-actions">
            <el-button 
              size="mini" 
              type="text" 
              :class="{ active: reply.isLiked }"
              @click="toggleReplyLike(reply)"
            >
              <i class="el-icon-star-off"></i> {{ reply.likes }}
            </el-button>
            <el-button 
              size="mini" 
              type="text"
              @click="quoteReply(reply)"
            >
              <i class="el-icon-chat-dot-square"></i> 引用
            </el-button>
          </div>
        </div>
      </el-card>
      
      <el-pagination
        v-if="replies.length > 10"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-size="10"
        layout="prev, pager, next"
        :total="replies.length"
        class="pagination"
      ></el-pagination>
    </div>
    
    <el-card class="reply-form-card" shadow="hover">
      <h3 class="form-title">发表回复</h3>
      
      <el-form ref="replyForm" :model="replyForm">
        <el-form-item>
          <div class="editor-toolbar">
            <el-button-group>
              <el-button size="small" icon="el-icon-bold">粗体</el-button>
              <el-button size="small" icon="el-icon-italic">斜体</el-button>
              <el-button size="small" icon="el-icon-link">链接</el-button>
              <el-button size="small" icon="el-icon-picture">图片</el-button>
              <el-button size="small" icon="el-icon-s-grid">表格</el-button>
              <el-button size="small" icon="el-icon-s-order">代码</el-button>
            </el-button-group>
          </div>
          
          <el-input
            type="textarea"
            :rows="6"
            placeholder="请输入回复内容..."
            v-model="replyForm.content"
          ></el-input>
        </el-form-item>
        
        <el-form-item>
          <div class="form-footer">
            <div class="upload-attachments">
              <el-upload
                action="#"
                :auto-upload="false"
                :on-change="handleAttachmentChange"
                :file-list="replyForm.attachments"
                multiple
              >
                <el-button size="small" icon="el-icon-paperclip">添加附件</el-button>
              </el-upload>
            </div>
            
            <el-button type="primary" @click="submitReply">发表回复</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'ForumTopic',
  data() {
    return {
      topicId: null,
      replySort: 'earliest',
      currentPage: 1,
      replyForm: {
        content: '',
        attachments: []
      },
      
      // 模拟数据，实际应从API获取
      topic: {
        id: 1,
        title: '新型高熵合金的结构稳定性讨论',
        category: 'materials',
        content: `<p>近期研究了几种新型高熵合金的相稳定性，发现温度对其微观结构有显著影响。特别是在Al-Co-Cr-Fe-Ni系统中，当铝含量超过15%时，合金表现出明显的相分离现象。</p>
                  <p>我们通过XRD和TEM观察到，在750°C退火后，原本单一的FCC相逐渐转变为FCC+BCC两相共存结构。这一现象与之前的文献报道不太一致，可能与我们采用的制备工艺有关。</p>
                  <p>想请教各位专家，在高熵合金设计中，如何更好地预测和控制相稳定性？特别是在温度波动较大的应用场景下，如何确保合金性能的一致性？</p>
                  <p>附件中是我们的实验数据和一些显微照片，希望能得到大家的建议。</p>`,
        createdAt: '2023-06-15T09:30:00',
        author: {
          id: 101,
          name: '李研究员',
          title: '材料科学高级研究员',
          avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
        },
        views: 328,
        likes: 46,
        isLiked: false,
        tags: ['高熵合金', '相稳定性', '微观结构', '热处理'],
        attachments: [
          {
            id: 1001,
            name: 'HEA_XRD_Data.xlsx',
            type: 'excel',
            size: 2.4 * 1024 * 1024 // 2.4MB
          },
          {
            id: 1002,
            name: 'TEM_Images.zip',
            type: 'archive',
            size: 15.7 * 1024 * 1024 // 15.7MB
          }
        ]
      },
      
      replies: [
        {
          id: 101,
          content: `<p>您观察到的相分离现象在高熵合金中确实很常见，特别是含铝量较高的体系。</p>
                    <p>我建议从热力学角度考虑这个问题。Al元素是强BCC形成元素，当含量增加时，体系会倾向于形成BCC相。您可以尝试使用Thermo-Calc等软件进行平衡相计算，预测不同温度下的相组成。</p>
                    <p>另外，制备工艺也确实会影响最终的相结构。快冷工艺（如铜模吸铸）可能会"冻结"亚稳相，导致热处理后的相转变行为与平衡状态有所差异。</p>`,
          createdAt: '2023-06-15T10:45:00',
          author: {
            id: 105,
            name: '张教授',
            title: '材料热力学专家',
            avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
          },
          likes: 28,
          isLiked: true,
          attachments: []
        },
        {
          id: 102,
          content: `<p>从您分享的数据来看，退火过程中的冷却速率似乎对最终相结构有显著影响。我注意到750°C退火后空冷和炉冷的样品XRD图谱有明显差异。</p>
                    <p>在我们实验室的研究中，我们发现高熵合金中BCC和FCC相的转变动力学较慢，有时需要更长的退火时间才能达到平衡状态。您可以尝试延长退火时间，或者进行分步退火处理。</p>
                    <p>另外，建议您也测量一下不同相的成分，看看是否存在元素分配不均的情况，这对理解相稳定性机制很有帮助。</p>`,
          createdAt: '2023-06-15T14:20:00',
          author: {
            id: 102,
            name: '王博士',
            title: '金属材料研究员',
            avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
          },
          likes: 15,
          isLiked: false,
          attachments: [
            {
              id: 2001,
              name: 'HEA_PhaseTransformation.pdf',
              type: 'pdf',
              size: 3.8 * 1024 * 1024 // 3.8MB
            }
          ]
        },
        {
          id: 103,
          content: `<p>我们最近在类似体系中也观察到了相似的现象。通过一系列的原位TEM观察，发现相分离是通过经典的形核-长大机制进行的，而不是通过自发的纺锤形分解。</p>
                    <p>对于温度波动条件下的稳定性控制，我建议考虑添加少量的稳定化元素，如Ti或Nb，它们能够显著提高高温下的相稳定性。</p>
                    <p>另外，HEA的相稳定性往往与电子构型有关，您可以计算一下价电子浓度(VEC)，如果在6.8-7.5之间，可能更倾向于形成稳定的单相结构。</p>`,
          createdAt: '2023-06-16T09:15:00',
          author: {
            id: 107,
            name: '刘教授',
            title: '高温合金专家',
            avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
          },
          likes: 23,
          isLiked: false,
          attachments: []
        },
        {
          id: 104,
          content: `<p>从计算模拟的角度，我想补充一点。我们最近使用第一性原理结合蒙特卡洛方法对类似体系进行了模拟，发现即使在"高熵"状态，局部的短程有序结构仍然可能存在，这可能是相分离的先驱体。</p>
                    <p>我对您的TEM图像特别感兴趣。从图片中可以看到纳米尺度的调幅结构，这很可能与局部元素富集有关。建议您进行原子探针(APT)分析，可以获得更精确的元素分布信息。</p>`,
          createdAt: '2023-06-17T11:40:00',
          author: {
            id: 106,
            name: '赵博士',
            title: '计算材料学专家',
            avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
          },
          likes: 19,
          isLiked: false,
          attachments: []
        }
      ]
    }
  },
  computed: {
    sortedReplies() {
      const repliesCopy = [...this.replies];
      
      if (this.replySort === 'latest') {
        return repliesCopy.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      } else if (this.replySort === 'popular') {
        return repliesCopy.sort((a, b) => b.likes - a.likes);
      } else { // earliest
        return repliesCopy.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
      }
    },
    
    paginatedReplies() {
      const start = (this.currentPage - 1) * 10;
      const end = start + 10;
      return this.sortedReplies.slice(start, end);
    }
  },
  created() {
    this.topicId = this.$route.params.id;
    this.fetchTopicDetails();
  },
  methods: {
    fetchTopicDetails() {
      // 在实际项目中，这里会根据this.topicId发送API请求
      // 获取话题详情和回复列表
      console.log('加载话题ID:', this.topicId);
      // 此处使用模拟数据
    },
    
    goBack() {
      this.$router.push('/forum');
    },
    
    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    
    getCategoryName(category) {
      const categories = {
        'materials': '材料科学',
        'computation': '计算模拟',
        'characterization': '表征技术',
        'application': '应用技术'
      };
      return categories[category] || category;
    },
    
    getCategoryTagType(category) {
      const types = {
        'materials': '',
        'computation': 'success',
        'characterization': 'warning',
        'application': 'info'
      };
      return types[category] || '';
    },
    
    getAttachmentIcon(type) {
      const icons = {
        'pdf': 'el-icon-document',
        'excel': 'el-icon-s-grid',
        'word': 'el-icon-document-copy',
        'image': 'el-icon-picture',
        'archive': 'el-icon-folder',
        'video': 'el-icon-video-camera'
      };
      return icons[type] || 'el-icon-document';
    },
    
    formatSize(bytes) {
      if (bytes === 0) return '0 B';
      
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    handleCurrentChange(page) {
      this.currentPage = page;
    },
    
    sortReplies(sort) {
      // 在实际项目中，这里可能会重新请求排序后的数据
      // 或直接在前端进行排序（已通过计算属性实现）
    },
    
    toggleLike() {
      this.topic.isLiked = !this.topic.isLiked;
      this.topic.likes += this.topic.isLiked ? 1 : -1;
      
      // 在实际项目中，这里会调用API更新点赞状态
    },
    
    toggleReplyLike(reply) {
      reply.isLiked = !reply.isLiked;
      reply.likes += reply.isLiked ? 1 : -1;
      
      // 在实际项目中，这里会调用API更新回复点赞状态
    },
    
    collectTopic() {
      this.$message({
        message: '成功收藏该话题',
        type: 'success'
      });
      
      // 在实际项目中，这里会调用API更新收藏状态
    },
    
    shareTopic() {
      // 在实际项目中，这里会显示分享对话框或生成分享链接
      this.$message({
        message: '话题链接已复制到剪贴板',
        type: 'success'
      });
    },
    
    downloadAttachment(attachment) {
      // 在实际项目中，这里会调用API下载附件
      this.$message({
        message: `正在下载 ${attachment.name}...`,
        type: 'info'
      });
    },
    
    handleAttachmentChange(file, fileList) {
      this.replyForm.attachments = fileList;
    },
    
    quoteReply(reply) {
      const quoteContent = `<blockquote>
        <p><strong>${reply.author.name} 写道：</strong></p>
        ${reply.content}
      </blockquote>
      <p></p>`;
      
      this.replyForm.content = quoteContent + this.replyForm.content;
      
      // 滚动到回复区域
      this.$nextTick(() => {
        const replyFormEl = document.querySelector('.reply-form-card');
        if (replyFormEl) {
          replyFormEl.scrollIntoView({ behavior: 'smooth' });
        }
      });
    },
    
    submitReply() {
      if (!this.replyForm.content.trim()) {
        this.$message({
          message: '请输入回复内容',
          type: 'warning'
        });
        return;
      }
      
      // 在实际项目中，这里会调用API提交回复
      this.$message({
        message: '成功发表回复',
        type: 'success'
      });
      
      // 模拟添加新回复
      const newReply = {
        id: Date.now(),
        content: this.replyForm.content,
        createdAt: new Date().toISOString(),
        author: {
          id: 999, // 假设当前用户ID
          name: '当前用户',
          title: '材料爱好者',
          avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
        },
        likes: 0,
        isLiked: false,
        attachments: this.replyForm.attachments.map(file => ({
          id: Date.now() + Math.floor(Math.random() * 1000),
          name: file.name,
          type: this.getFileType(file.name),
          size: file.size
        }))
      };
      
      this.replies.push(newReply);
      
      // 清空表单
      this.replyForm = {
        content: '',
        attachments: []
      };
      
      // 滚动到新回复
      this.$nextTick(() => {
        window.scrollTo({
          top: document.body.scrollHeight,
          behavior: 'smooth'
        });
      });
    },
    
    getFileType(fileName) {
      const extension = fileName.split('.').pop().toLowerCase();
      const typeMap = {
        'pdf': 'pdf',
        'doc': 'word',
        'docx': 'word',
        'xls': 'excel',
        'xlsx': 'excel',
        'jpg': 'image',
        'jpeg': 'image',
        'png': 'image',
        'gif': 'image',
        'zip': 'archive',
        'rar': 'archive',
        '7z': 'archive',
        'mp4': 'video',
        'avi': 'video',
        'mov': 'video'
      };
      
      return typeMap[extension] || 'document';
    }
  }
}
</script>

<style scoped lang="scss">
.forum-topic-container {
  padding: 20px;
  
  .back-button {
    display: inline-block;
    cursor: pointer;
    margin-bottom: 15px;
    color: #409EFF;
    font-size: 16px;
    
    &:hover {
      text-decoration: underline;
    }
    
    i {
      margin-right: 5px;
    }
  }
  
  .topic-card {
    margin-bottom: 20px;
    
    .topic-header {
      margin-bottom: 20px;
      
      .topic-title {
        h1 {
          font-size: 24px;
          font-weight: 600;
          margin: 0 0 10px 0;
        }
        
        .topic-meta {
          display: flex;
          align-items: center;
          
          .el-tag {
            margin-right: 10px;
          }
          
          .topic-time {
            font-size: 14px;
            color: #909399;
          }
        }
      }
    }
    
    .topic-content {
      display: flex;
      margin-bottom: 20px;
      border-bottom: 1px solid #EBEEF5;
      padding-bottom: 20px;
      
      .author-info {
        width: 120px;
        margin-right: 20px;
        text-align: center;
        
        .author-details {
          margin-top: 10px;
          
          .author-name {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 5px;
          }
          
          .author-title {
            font-size: 12px;
            color: #909399;
          }
        }
      }
      
      .content-body {
        flex: 1;
        color: #303133;
        font-size: 15px;
        line-height: 1.8;
        
        ::v-deep p {
          margin-bottom: 15px;
        }
        
        .content-tags {
          margin-top: 20px;
          
          .tag-item {
            margin-right: 8px;
            margin-bottom: 8px;
          }
        }
        
        .content-attachments {
          margin-top: 20px;
          
          h3 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
          }
          
          .attachment-list {
            .attachment-item {
              display: inline-flex;
              align-items: center;
              padding: 8px 12px;
              background-color: #f5f7fa;
              border-radius: 4px;
              margin-right: 10px;
              margin-bottom: 10px;
              cursor: pointer;
              
              &:hover {
                background-color: #ecf5ff;
              }
              
              i {
                font-size: 18px;
                margin-right: 8px;
                color: #409EFF;
              }
              
              .attachment-name {
                margin-right: 5px;
              }
              
              .attachment-size {
                font-size: 12px;
                color: #909399;
              }
            }
          }
        }
      }
    }
    
    .topic-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .topic-stats {
        font-size: 14px;
        color: #606266;
        
        .stat-item {
          margin-left: 15px;
          
          i {
            margin-right: 5px;
          }
        }
      }
    }
  }
  
  .replies-section {
    margin-bottom: 20px;
    
    .section-title {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 15px;
    }
    
    .replies-filter {
      margin-bottom: 20px;
    }
    
    .reply-card {
      margin-bottom: 15px;
      
      .reply-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        
        .reply-info {
          display: flex;
          align-items: center;
          
          .reply-author {
            margin-left: 10px;
            
            .author-name {
              font-size: 16px;
              font-weight: 500;
              margin-bottom: 3px;
            }
            
            .author-title {
              font-size: 12px;
              color: #909399;
            }
          }
        }
        
        .reply-index {
          font-size: 14px;
          color: #909399;
        }
      }
      
      .reply-content {
        color: #303133;
        font-size: 14px;
        line-height: 1.8;
        margin-bottom: 15px;
        
        ::v-deep blockquote {
          background-color: #f5f7fa;
          border-left: 4px solid #dcdfe6;
          padding: 10px 15px;
          margin: 10px 0;
          color: #606266;
        }
        
        .reply-attachments {
          margin-top: 15px;
          
          .attachment-list {
            .attachment-item {
              display: inline-flex;
              align-items: center;
              padding: 6px 10px;
              background-color: #f5f7fa;
              border-radius: 4px;
              margin-right: 10px;
              margin-bottom: 10px;
              cursor: pointer;
              font-size: 13px;
              
              &:hover {
                background-color: #ecf5ff;
              }
              
              i {
                font-size: 16px;
                margin-right: 6px;
                color: #409EFF;
              }
              
              .attachment-name {
                margin-right: 5px;
              }
              
              .attachment-size {
                font-size: 12px;
                color: #909399;
              }
            }
          }
        }
      }
      
      .reply-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 13px;
        
        .reply-time {
          color: #909399;
        }
        
        .reply-actions {
          .el-button {
            margin-left: 10px;
            
            &.active {
              color: #409EFF;
            }
          }
        }
      }
    }
    
    .pagination {
      margin-top: 20px;
      text-align: center;
    }
  }
  
  .reply-form-card {
    .form-title {
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 15px 0;
    }
    
    .editor-toolbar {
      margin-bottom: 10px;
    }
    
    .form-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .el-button {
        margin-left: 15px;
      }
    }
  }
}

@media (max-width: 768px) {
  .forum-topic-container {
    .topic-content {
      flex-direction: column;
      
      .author-info {
        width: 100%;
        display: flex;
        align-items: center;
        text-align: left;
        margin-bottom: 15px;
        
        .author-details {
          margin-top: 0;
          margin-left: 15px;
        }
      }
    }
    
    .topic-actions {
      flex-direction: column;
      
      .action-buttons {
        margin-bottom: 15px;
      }
      
      .topic-stats {
        align-self: flex-start;
      }
    }
    
    .reply-form-card {
      .form-footer {
        flex-direction: column;
        
        .upload-attachments {
          margin-bottom: 15px;
        }
        
        .el-button {
          margin-left: 0;
        }
      }
    }
  }
}
</style>