const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const { createServer } = require('http');
const { Server } = require('socket.io');
require('dotenv').config();

const config = require('./config');

console.log('开始加载应用...');

const app = express();
const server = createServer(app);

console.log('Express应用创建成功');

// Socket.IO配置
const io = new Server(server, {
  cors: {
    origin: config.cors.origin,
    credentials: config.cors.credentials
  }
});

console.log('Socket.IO配置完成');

// 全局中间件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
}));

app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: config.cors.credentials,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

console.log('中间件配置完成');

// 静态文件服务
app.use('/uploads', express.static('uploads'));

// 健康检查
app.get('/health', (req, res) => {
  console.log('健康检查请求');
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: config.app.env,
    version: process.env.npm_package_version || '1.0.0'
  });
});

// 简单的API测试路由
app.get('/api/test', (req, res) => {
  console.log('API测试请求');
  res.json({ message: 'API is working', timestamp: new Date().toISOString() });
});

console.log('路由配置完成');

// Socket.IO事件处理
io.on('connection', (socket) => {
  console.log(`用户连接: ${socket.id}`);

  socket.on('join', (data) => {
    if (data.userId) {
      socket.join(`user_${data.userId}`);
      console.log(`用户 ${data.userId} 加入房间`);
    }
  });

  socket.on('disconnect', () => {
    console.log(`用户断开连接: ${socket.id}`);
  });
});

// 将io实例添加到app中，供其他模块使用
app.set('io', io);

console.log('Socket.IO事件处理配置完成');

// 启动服务器
const PORT = config.app.port || 8001;

async function startServer() {
  try {
    console.log('开始启动服务器...');
    
    console.log(`准备在端口 ${PORT} 启动服务器...`);
    
    server.listen(PORT, (err) => {
      if (err) {
        console.error('服务器启动失败:', err);
        process.exit(1);
        return;
      }
      
      console.log(`✅ 服务器启动成功，端口: ${PORT}`);
      console.log(`✅ 环境: ${config.app.env}`);
      console.log(`✅ 前端地址: http://localhost:3000`);
      console.log(`✅ 后端地址: http://localhost:${PORT}`);
      console.log(`✅ 健康检查: http://localhost:${PORT}/health`);
      console.log(`✅ API测试: http://localhost:${PORT}/api/test`);
    });
    
    server.on('error', (error) => {
      console.error('服务器错误:', error);
      if (error.code === 'EADDRINUSE') {
        console.error(`端口 ${PORT} 已被占用`);
      }
      process.exit(1);
    });
    
  } catch (error) {
    console.error('服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', async () => {
  console.log('收到SIGTERM信号，开始优雅关闭...');
  
  server.close(() => {
    console.log('HTTP服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  console.log('收到SIGINT信号，开始优雅关闭...');
  
  server.close(() => {
    console.log('HTTP服务器已关闭');
    process.exit(0);
  });
});

// 导出app对象和启动函数
module.exports = { app, startServer };

// 直接启动服务器（当直接运行此文件时）
if (require.main === module) {
  console.log('直接运行模式，启动服务器...');
  startServer().catch(error => {
    console.error('启动服务器时发生错误:', error);
    process.exit(1);
  });
}

console.log('应用配置完成，等待启动...');
