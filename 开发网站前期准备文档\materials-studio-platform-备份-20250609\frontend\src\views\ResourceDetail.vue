<template>
  <div class="resource-detail-container">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/resources' }">资源中心</el-breadcrumb-item>
      <el-breadcrumb-item>{{ resource.title }}</el-breadcrumb-item>
    </el-breadcrumb>

    <div class="detail-header">
      <h1>{{ resource.title }}</h1>
      <div class="meta-info">
        <span><i class="el-icon-user"></i> {{ resource.author }}</span>
        <span><i class="el-icon-date"></i> {{ formatDate(resource.createdAt) }}</span>
        <span><i class="el-icon-view"></i> {{ resource.views }} 次查看</span>
        <span><i class="el-icon-download"></i> {{ resource.downloads }} 次下载</span>
      </div>
      <div class="tags">
        <el-tag v-for="tag in resource.tags" :key="tag" size="small">{{ tag }}</el-tag>
      </div>
    </div>

    <el-divider></el-divider>

    <el-row :gutter="20">
      <el-col :xs="24" :md="resource.type === 'structure' ? 16 : 24">
        <div class="detail-content">
          <div class="description" v-html="resource.description"></div>
          
          <!-- 根据资源类型显示不同内容 -->
          <div v-if="resource.type === 'document'" class="document-preview">
            <el-card>
              <iframe :src="resource.previewUrl" width="100%" height="600"></iframe>
            </el-card>
          </div>

          <div v-else-if="resource.type === 'video'" class="video-container">
            <el-card>
              <video-player :src="resource.videoUrl" :poster="resource.posterUrl"></video-player>
            </el-card>
          </div>

          <div v-else-if="resource.type === 'structure'" class="structure-data">
            <el-tabs type="border-card">
              <el-tab-pane label="基本信息">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="化学式">{{ resource.formula }}</el-descriptions-item>
                  <el-descriptions-item label="空间群">{{ resource.spaceGroup }}</el-descriptions-item>
                  <el-descriptions-item label="晶格常数">
                    a = {{ resource.lattice.a }} Å, 
                    b = {{ resource.lattice.b }} Å, 
                    c = {{ resource.lattice.c }} Å
                  </el-descriptions-item>
                  <el-descriptions-item label="晶格角度">
                    α = {{ resource.lattice.alpha }}°, 
                    β = {{ resource.lattice.beta }}°, 
                    γ = {{ resource.lattice.gamma }}°
                  </el-descriptions-item>
                  <el-descriptions-item label="密度">{{ resource.density }} g/cm³</el-descriptions-item>
                  <el-descriptions-item label="体积">{{ resource.volume }} Å³</el-descriptions-item>
                  <el-descriptions-item label="数据来源">{{ resource.source }}</el-descriptions-item>
                  <el-descriptions-item label="计算方法">{{ resource.method || '实验测量' }}</el-descriptions-item>
                </el-descriptions>
              </el-tab-pane>
              
              <el-tab-pane label="原子坐标">
                <el-table :data="resource.atoms" style="width: 100%">
                  <el-table-column prop="element" label="元素" width="100"></el-table-column>
                  <el-table-column prop="x" label="x" width="120"></el-table-column>
                  <el-table-column prop="y" label="y" width="120"></el-table-column>
                  <el-table-column prop="z" label="z" width="120"></el-table-column>
                  <el-table-column prop="occupancy" label="占据率" width="120"></el-table-column>
                  <el-table-column prop="wyckoff" label="Wyckoff位置"></el-table-column>
                </el-table>
              </el-tab-pane>
              
              <el-tab-pane label="物理性质" v-if="resource.properties">
                <el-collapse>
                  <el-collapse-item title="电子性质" name="electronic" v-if="resource.properties.electronic">
                    <el-descriptions :column="2" border>
                      <el-descriptions-item label="带隙">{{ resource.properties.electronic.bandGap }} eV</el-descriptions-item>
                      <el-descriptions-item label="导带最小值">{{ resource.properties.electronic.cbm }} eV</el-descriptions-item>
                      <el-descriptions-item label="价带最大值">{{ resource.properties.electronic.vbm }} eV</el-descriptions-item>
                      <el-descriptions-item label="带隙类型">{{ resource.properties.electronic.bandGapType }}</el-descriptions-item>
                    </el-descriptions>
                  </el-collapse-item>
                  
                  <el-collapse-item title="力学性质" name="mechanical" v-if="resource.properties.mechanical">
                    <el-descriptions :column="2" border>
                      <el-descriptions-item label="体积模量">{{ resource.properties.mechanical.bulkModulus }} GPa</el-descriptions-item>
                      <el-descriptions-item label="剪切模量">{{ resource.properties.mechanical.shearModulus }} GPa</el-descriptions-item>
                      <el-descriptions-item label="杨氏模量">{{ resource.properties.mechanical.youngsModulus }} GPa</el-descriptions-item>
                      <el-descriptions-item label="泊松比">{{ resource.properties.mechanical.poissonRatio }}</el-descriptions-item>
                    </el-descriptions>
                  </el-collapse-item>
                  
                  <el-collapse-item title="热力学性质" name="thermal" v-if="resource.properties.thermal">
                    <el-descriptions :column="2" border>
                      <el-descriptions-item label="形成能">{{ resource.properties.thermal.formationEnergy }} eV/atom</el-descriptions-item>
                      <el-descriptions-item label="热膨胀系数">{{ resource.properties.thermal.thermalExpansion }} K⁻¹</el-descriptions-item>
                    </el-descriptions>
                  </el-collapse-item>
                </el-collapse>
              </el-tab-pane>
              
              <el-tab-pane label="CIF文件">
                <pre class="cif-content">{{ resource.cifContent }}</pre>
                <div class="download-actions">
                  <el-button type="primary" icon="el-icon-download" @click="downloadCif">下载CIF文件</el-button>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="24" :md="8" v-if="resource.type === 'structure'">
        <div class="structure-viewer">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>结构可视化</span>
                <el-dropdown trigger="click" @command="handleViewMode">
                  <el-button type="text">
                    视图模式<i class="el-icon-arrow-down"></i>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="ball-stick">球棍模型</el-dropdown-item>
                      <el-dropdown-item command="space-filling">空间填充模型</el-dropdown-item>
                      <el-dropdown-item command="polyhedral">多面体模型</el-dropdown-item>
                      <el-dropdown-item command="wireframe">线框模型</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
            
            <div class="viewer-container">
              <!-- 此处将集成3D可视化组件，如3Dmol.js或JSmol -->
              <div id="structure-viewer-container" style="width: 100%; height: 400px;"></div>
              
              <div class="viewer-controls">
                <el-button-group>
                  <el-button icon="el-icon-refresh-left" size="small" @click="rotateStructure('left')"></el-button>
                  <el-button icon="el-icon-refresh-right" size="small" @click="rotateStructure('right')"></el-button>
                </el-button-group>
                <el-button-group>
                  <el-button icon="el-icon-zoom-in" size="small" @click="zoomStructure(1.2)"></el-button>
                  <el-button icon="el-icon-zoom-out" size="small" @click="zoomStructure(0.8)"></el-button>
                </el-button-group>
                <el-button icon="el-icon-refresh" size="small" @click="resetView"></el-button>
              </div>
              
              <div class="display-options">
                <el-checkbox v-model="displayOptions.showUnitCell">显示晶胞</el-checkbox>
                <el-checkbox v-model="displayOptions.showLabels">显示原子标签</el-checkbox>
                <el-slider v-model="displayOptions.transparency" :min="0" :max="100" :format-tooltip="formatTooltip"></el-slider>
              </div>
            </div>
          </el-card>
          
          <!-- 相关资源推荐 -->
          <el-card class="related-resources" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>相关资源</span>
              </div>
            </template>
            <div v-if="relatedResources.length > 0">
              <div v-for="item in relatedResources" :key="item.id" class="related-item">
                <el-link :underline="false" @click="goToResource(item.id)">{{ item.title }}</el-link>
                <div class="related-meta">
                  <span>{{ item.type === 'structure' ? '结构数据' : item.type === 'document' ? '文档' : '视频' }}</span>
                  <span>{{ formatDate(item.createdAt) }}</span>
                </div>
              </div>
            </div>
            <el-empty v-else description="暂无相关资源"></el-empty>
          </el-card>
        </div>
      </el-col>
    </el-row>
    
    <div class="action-btns">
      <el-button type="primary" icon="el-icon-download" @click="downloadResource">下载资源</el-button>
      <el-button type="success" icon="el-icon-star-off" @click="collectResource" :disabled="isCollected">
        {{ isCollected ? '已收藏' : '收藏' }}
      </el-button>
      <el-button type="info" icon="el-icon-share" @click="shareResource">分享</el-button>
    </div>
    
    <!-- 评论区 -->
    <div class="comments-section">
      <h3>评论 ({{ comments.length }})</h3>
      <div class="comment-form">
        <el-input
          type="textarea"
          :rows="3"
          placeholder="请输入您的评论..."
          v-model="commentContent"
        ></el-input>
        <div class="comment-actions">
          <el-button type="primary" @click="submitComment" :disabled="!commentContent.trim()">提交评论</el-button>
        </div>
      </div>
      
      <div class="comments-list">
        <div v-for="comment in comments" :key="comment.id" class="comment-item">
          <div class="comment-header">
            <div class="user-info">
              <el-avatar :size="40" :src="comment.user.avatar"></el-avatar>
              <div class="user-meta">
                <div class="username">{{ comment.user.username }}</div>
                <div class="comment-time">{{ formatDate(comment.createdAt) }}</div>
              </div>
            </div>
            <div class="comment-actions">
              <el-button type="text" @click="replyToComment(comment.id)">回复</el-button>
            </div>
          </div>
          <div class="comment-content">{{ comment.content }}</div>
          
          <!-- 回复列表 -->
          <div class="replies-list" v-if="comment.replies && comment.replies.length > 0">
            <div v-for="reply in comment.replies" :key="reply.id" class="reply-item">
              <div class="reply-header">
                <div class="user-info">
                  <el-avatar :size="30" :src="reply.user.avatar"></el-avatar>
                  <div class="user-meta">
                    <div class="username">{{ reply.user.username }}</div>
                    <div class="reply-time">{{ formatDate(reply.createdAt) }}</div>
                  </div>
                </div>
              </div>
              <div class="reply-content">{{ reply.content }}</div>
            </div>
          </div>
          
          <!-- 回复框 -->
          <div class="reply-form" v-if="replyingTo === comment.id">
            <el-input
              type="textarea"
              :rows="2"
              placeholder="请输入您的回复..."
              v-model="replyContent"
            ></el-input>
            <div class="reply-actions">
              <el-button size="small" @click="cancelReply">取消</el-button>
              <el-button type="primary" size="small" @click="submitReply" :disabled="!replyContent.trim()">回复</el-button>
            </div>
          </div>
        </div>
        
        <el-empty v-if="comments.length === 0" description="暂无评论"></el-empty>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
// 假设已有视频播放组件
// import VideoPlayer from '@/components/VideoPlayer.vue'

export default {
  name: 'ResourceDetail',
  // components: {
  //   VideoPlayer
  // },
  setup() {
    const route = useRoute()
    const router = useRouter()
    
    // 资源数据
    const resource = ref({
      id: '1',
      title: 'SiO2晶体结构数据',
      type: 'structure',
      author: '材料研究所',
      createdAt: '2023-06-15T08:30:00Z',
      views: 1234,
      downloads: 456,
      tags: ['二氧化硅', '晶体结构', '热力学数据'],
      description: '<p>二氧化硅(SiO2)是一种重要的无机材料，广泛应用于半导体、陶瓷、玻璃等领域。本资源提供了SiO2的晶体结构数据，包括原子坐标、晶格参数及相关物理性质。</p>',
      formula: 'SiO2',
      spaceGroup: 'P3221 (No. 154)',
      lattice: {
        a: 4.913,
        b: 4.913,
        c: 5.405,
        alpha: 90,
        beta: 90,
        gamma: 120
      },
      density: 2.65,
      volume: 113.01,
      source: 'Materials Project',
      method: 'DFT-PBE',
      atoms: [
        { element: 'Si', x: 0.4699, y: 0.0000, z: 0.3333, occupancy: 1.0, wyckoff: '3a' },
        { element: 'O', x: 0.4141, y: 0.2681, z: 0.2143, occupancy: 1.0, wyckoff: '6c' }
      ],
      properties: {
        electronic: {
          bandGap: 5.7,
          cbm: 4.2,
          vbm: -1.5,
          bandGapType: '直接带隙'
        },
        mechanical: {
          bulkModulus: 36.8,
          shearModulus: 31.1,
          youngsModulus: 73.2,
          poissonRatio: 0.17
        },
        thermal: {
          formationEnergy: -5.97,
          thermalExpansion: 0.55e-6
        }
      },
      cifContent: `data_SiO2
_chemical_formula_sum 'Si O2'
_cell_length_a 4.913
_cell_length_b 4.913
_cell_length_c 5.405
_cell_angle_alpha 90
_cell_angle_beta 90
_cell_angle_gamma 120
_symmetry_space_group_name_H-M 'P 32 2 1'
loop_
_atom_site_label
_atom_site_type_symbol
_atom_site_fract_x
_atom_site_fract_y
_atom_site_fract_z
_atom_site_occupancy
Si1 Si 0.4699 0.0000 0.3333 1.0
O1 O 0.4141 0.2681 0.2143 1.0
O2 O 0.2681 0.4141 0.7857 1.0`
    })
    
    // 相关资源
    const relatedResources = ref([
      { id: '2', title: 'SiO2多晶型及相变研究', type: 'document', createdAt: '2023-05-20T10:15:00Z' },
      { id: '3', title: '二氧化硅材料应用进展', type: 'document', createdAt: '2023-04-12T14:25:00Z' },
      { id: '4', title: 'Al2O3晶体结构数据', type: 'structure', createdAt: '2023-03-05T09:40:00Z' }
    ])
    
    // 评论数据
    const comments = ref([
      {
        id: '1',
        content: '这个结构数据非常有用，我正在研究SiO2材料的热学性质，这些数据对我帮助很大。',
        createdAt: '2023-06-18T14:30:00Z',
        user: {
          username: '材料研究者A',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
        },
        replies: [
          {
            id: '2',
            content: '您可以结合我们平台上的热物性数据库，会有更多发现。',
            createdAt: '2023-06-18T15:45:00Z',
            user: {
              username: '平台管理员',
              avatar: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png'
            }
          }
        ]
      }
    ])
    
    // 结构可视化相关
    const displayOptions = reactive({
      showUnitCell: true,
      showLabels: false,
      transparency: 0
    })
    
    // 评论功能相关
    const commentContent = ref('')
    const replyContent = ref('')
    const replyingTo = ref(null)
    const isCollected = ref(false)
    
    // 方法
    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    }
    
    const handleViewMode = (command) => {
      console.log(`切换到${command}视图模式`)
      // 实际项目中这里会调用3D渲染库的API来切换视图模式
    }
    
    const rotateStructure = (direction) => {
      console.log(`向${direction}方向旋转结构`)
      // 实际项目中会调用3D渲染库的API
    }
    
    const zoomStructure = (factor) => {
      console.log(`缩放结构，因子: ${factor}`)
      // 实际项目中会调用3D渲染库的API
    }
    
    const resetView = () => {
      console.log('重置视图')
      // 实际项目中会调用3D渲染库的API
    }
    
    const formatTooltip = (val) => {
      return `透明度: ${val}%`
    }
    
    const downloadCif = () => {
      // 创建Blob对象并下载
      const blob = new Blob([resource.value.cifContent], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${resource.value.formula}.cif`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
    
    const downloadResource = () => {
      console.log('下载资源')
      // 实际项目中会调用API进行下载
    }
    
    const collectResource = () => {
      isCollected.value = true
      console.log('收藏资源')
      // 实际项目中会调用API进行收藏
    }
    
    const shareResource = () => {
      console.log('分享资源')
      // 实际项目中会显示分享对话框
    }
    
    const goToResource = (id) => {
      router.push(`/resources/${id}`)
    }
    
    const submitComment = () => {
      // 提交评论
      const newComment = {
        id: Date.now().toString(),
        content: commentContent.value,
        createdAt: new Date().toISOString(),
        user: {
          username: '当前用户',
          avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
        },
        replies: []
      }
      comments.value.unshift(newComment)
      commentContent.value = ''
    }
    
    const replyToComment = (commentId) => {
      replyingTo.value = commentId
    }
    
    const cancelReply = () => {
      replyingTo.value = null
      replyContent.value = ''
    }
    
    const submitReply = () => {
      // 提交回复
      const comment = comments.value.find(c => c.id === replyingTo.value)
      if (comment) {
        if (!comment.replies) {
          comment.replies = []
        }
        comment.replies.push({
          id: Date.now().toString(),
          content: replyContent.value,
          createdAt: new Date().toISOString(),
          user: {
            username: '当前用户',
            avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
          }
        })
        replyContent.value = ''
        replyingTo.value = null
      }
    }
    
    onMounted(() => {
      // 在实际项目中，这里会初始化3D结构查看器
      console.log('初始化3D结构查看器')
      // 加载资源数据
      const resourceId = route.params.id
      console.log(`加载资源ID: ${resourceId}`)
      // 实际项目中会调用API获取资源详情
    })
    
    return {
      resource,
      relatedResources,
      comments,
      displayOptions,
      commentContent,
      replyContent,
      replyingTo,
      isCollected,
      formatDate,
      handleViewMode,
      rotateStructure,
      zoomStructure,
      resetView,
      formatTooltip,
      downloadCif,
      downloadResource,
      collectResource,
      shareResource,
      goToResource,
      submitComment,
      replyToComment,
      cancelReply,
      submitReply
    }
  }
}
</script>

<style scoped lang="scss">
.resource-detail-container {
  padding: 20px;
  
  .detail-header {
    margin: 20px 0;
    
    h1 {
      margin-bottom: 15px;
      font-size: 24px;
      font-weight: 600;
    }
    
    .meta-info {
      margin-bottom: 15px;
      
      span {
        margin-right: 20px;
        color: #666;
        font-size: 14px;
        
        i {
          margin-right: 5px;
        }
      }
    }
    
    .tags {
      margin-top: 10px;
      
      .el-tag {
        margin-right: 8px;
        margin-bottom: 8px;
      }
    }
  }
  
  .detail-content {
    margin-bottom: 30px;
    
    .description {
      margin-bottom: 20px;
      line-height: 1.6;
    }
    
    .document-preview, .video-container {
      margin-top: 20px;
    }
    
    .structure-data {
      margin-top: 20px;
      
      .cif-content {
        background-color: #f8f8f8;
        padding: 15px;
        border-radius: 4px;
        font-family: monospace;
        white-space: pre-wrap;
        max-height: 300px;
        overflow-y: auto;
      }
      
      .download-actions {
        margin-top: 15px;
        text-align: right;
      }
    }
  }
  
  .structure-viewer {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .viewer-container {
      #structure-viewer-container {
        background-color: #f0f0f0;
        border-radius: 4px;
        margin-bottom: 15px;
      }
      
      .viewer-controls {
        display: flex;
        justify-content: center;
        margin-bottom: 15px;
        
        .el-button-group {
          margin-right: 10px;
        }
      }
      
      .display-options {
        margin-top: 15px;
        
        .el-checkbox {
          margin-right: 15px;
          margin-bottom: 10px;
        }
        
        .el-slider {
          margin-top: 10px;
        }
      }
    }
  }
  
  .related-resources {
    margin-top: 20px;
    
    .related-item {
      padding: 10px 0;
      border-bottom: 1px solid #eee;
      
      &:last-child {
        border-bottom: none;
      }
      
      .el-link {
        font-size: 14px;
        margin-bottom: 5px;
        display: block;
      }
      
      .related-meta {
        font-size: 12px;
        color: #999;
        
        span {
          margin-right: 10px;
        }
      }
    }
  }
  
  .action-btns {
    margin: 20px 0;
    display: flex;
    gap: 10px;
  }
  
  .comments-section {
    margin-top: 30px;
    
    h3 {
      margin-bottom: 20px;
      font-size: 18px;
      font-weight: 600;
    }
    
    .comment-form {
      margin-bottom: 30px;
      
      .comment-actions {
        margin-top: 10px;
        text-align: right;
      }
    }
    
    .comments-list {
      .comment-item {
        padding: 15px 0;
        border-bottom: 1px solid #eee;
        
        .comment-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
          
          .user-info {
            display: flex;
            align-items: center;
            
            .user-meta {
              margin-left: 10px;
              
              .username {
                font-weight: 600;
                font-size: 14px;
              }
              
              .comment-time {
                font-size: 12px;
                color: #999;
              }
            }
          }
        }
        
        .comment-content {
          margin: 10px 0;
          line-height: 1.5;
          font-size: 14px;
        }
        
        .replies-list {
          margin-left: 50px;
          
          .reply-item {
            padding: 10px 0;
            
            .reply-header {
              margin-bottom: 8px;
              
              .user-info {
                display: flex;
                align-items: center;
                
                .user-meta {
                  margin-left: 10px;
                  
                  .username {
                    font-weight: 600;
                    font-size: 13px;
                  }
                  
                  .reply-time {
                    font-size: 12px;
                    color: #999;
                  }
                }
              }
            }
            
            .reply-content {
              margin-left: 40px;
              font-size: 13px;
              line-height: 1.5;
            }
          }
        }
        
        .reply-form {
          margin-top: 15px;
          margin-left: 50px;
          
          .reply-actions {
            margin-top: 10px;
            text-align: right;
          }
        }
      }
    }
  }
}
</style> 