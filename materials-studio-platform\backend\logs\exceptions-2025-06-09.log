2025-06-09 12:01:48 [ERROR]: uncaughtException: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ]
  },
  "exception": true,
  "date": "Mon Jun 09 2025 12:01:48 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 11624,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 115384320,
      "heapTotal": 75427840,
      "heapUsed": 44988376,
      "external": 2292368,
      "arrayBuffers": 18815
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1733305.078
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1401,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "defaultResolveImpl",
      "line": 1057,
      "method": null,
      "native": false
    },
    {
      "column": 22,
      "file": "node:internal/modules/cjs/loader",
      "function": "resolveForCJSWithHooks",
      "line": 1062,
      "method": null,
      "native": false
    },
    {
      "column": 37,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1211,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1487,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 135,
      "method": null,
      "native": false
    },
    {
      "column": 16,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "function": null,
      "line": 4,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    }
  ]
}
2025-06-09 12:02:22 [ERROR]: uncaughtException: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ]
  },
  "exception": true,
  "date": "Mon Jun 09 2025 12:02:22 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 35000,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 118956032,
      "heapTotal": 75952128,
      "heapUsed": 44965488,
      "external": 2292368,
      "arrayBuffers": 18815
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1733339.921
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1401,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "defaultResolveImpl",
      "line": 1057,
      "method": null,
      "native": false
    },
    {
      "column": 22,
      "file": "node:internal/modules/cjs/loader",
      "function": "resolveForCJSWithHooks",
      "line": 1062,
      "method": null,
      "native": false
    },
    {
      "column": 37,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1211,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1487,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 135,
      "method": null,
      "native": false
    },
    {
      "column": 16,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "function": null,
      "line": 4,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    }
  ]
}
2025-06-09 12:14:39 [ERROR]: uncaughtException: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ]
  },
  "exception": true,
  "date": "Mon Jun 09 2025 12:14:39 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 31060,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 114995200,
      "heapTotal": 75165696,
      "heapUsed": 44991808,
      "external": 2292368,
      "arrayBuffers": 18815
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1734076.14
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1401,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "defaultResolveImpl",
      "line": 1057,
      "method": null,
      "native": false
    },
    {
      "column": 22,
      "file": "node:internal/modules/cjs/loader",
      "function": "resolveForCJSWithHooks",
      "line": 1062,
      "method": null,
      "native": false
    },
    {
      "column": 37,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1211,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1487,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 135,
      "method": null,
      "native": false
    },
    {
      "column": 16,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "function": null,
      "line": 4,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    }
  ]
}
2025-06-09 12:16:17 [ERROR]: uncaughtException: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ]
  },
  "exception": true,
  "date": "Mon Jun 09 2025 12:16:17 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 19128,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 115056640,
      "heapTotal": 75427840,
      "heapUsed": 44536752,
      "external": 2169237,
      "arrayBuffers": 18639
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1734174.234
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1401,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "defaultResolveImpl",
      "line": 1057,
      "method": null,
      "native": false
    },
    {
      "column": 22,
      "file": "node:internal/modules/cjs/loader",
      "function": "resolveForCJSWithHooks",
      "line": 1062,
      "method": null,
      "native": false
    },
    {
      "column": 37,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1211,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1487,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 135,
      "method": null,
      "native": false
    },
    {
      "column": 16,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "function": null,
      "line": 4,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    }
  ]
}
2025-06-09 12:16:41 [ERROR]: uncaughtException: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ]
  },
  "exception": true,
  "date": "Mon Jun 09 2025 12:16:41 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 7480,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 115068928,
      "heapTotal": 75427840,
      "heapUsed": 44785936,
      "external": 2169237,
      "arrayBuffers": 18639
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1734198.656
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1401,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "defaultResolveImpl",
      "line": 1057,
      "method": null,
      "native": false
    },
    {
      "column": 22,
      "file": "node:internal/modules/cjs/loader",
      "function": "resolveForCJSWithHooks",
      "line": 1062,
      "method": null,
      "native": false
    },
    {
      "column": 37,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1211,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1487,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 135,
      "method": null,
      "native": false
    },
    {
      "column": 16,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "function": null,
      "line": 4,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    }
  ]
}
2025-06-09 12:16:55 [ERROR]: uncaughtException: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ]
  },
  "exception": true,
  "date": "Mon Jun 09 2025 12:16:55 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 3208,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 115208192,
      "heapTotal": 75689984,
      "heapUsed": 44657616,
      "external": 2169237,
      "arrayBuffers": 18639
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1734212.765
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1401,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "defaultResolveImpl",
      "line": 1057,
      "method": null,
      "native": false
    },
    {
      "column": 22,
      "file": "node:internal/modules/cjs/loader",
      "function": "resolveForCJSWithHooks",
      "line": 1062,
      "method": null,
      "native": false
    },
    {
      "column": 37,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1211,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1487,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 135,
      "method": null,
      "native": false
    },
    {
      "column": 16,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "function": null,
      "line": 4,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    }
  ]
}
2025-06-09 12:18:51 [ERROR]: uncaughtException: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ]
  },
  "exception": true,
  "date": "Mon Jun 09 2025 12:18:51 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 12820,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 114544640,
      "heapTotal": 75165696,
      "heapUsed": 44917424,
      "external": 2169237,
      "arrayBuffers": 18639
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1734328.515
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1401,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "defaultResolveImpl",
      "line": 1057,
      "method": null,
      "native": false
    },
    {
      "column": 22,
      "file": "node:internal/modules/cjs/loader",
      "function": "resolveForCJSWithHooks",
      "line": 1062,
      "method": null,
      "native": false
    },
    {
      "column": 37,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1211,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1487,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 135,
      "method": null,
      "native": false
    },
    {
      "column": 16,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "function": null,
      "line": 4,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    }
  ]
}
2025-06-09 12:21:53 [ERROR]: uncaughtException: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ]
  },
  "exception": true,
  "date": "Mon Jun 09 2025 12:21:53 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 36832,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 120233984,
      "heapTotal": 74903552,
      "heapUsed": 45008936,
      "external": 2169237,
      "arrayBuffers": 18639
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1734510.093
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1401,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "defaultResolveImpl",
      "line": 1057,
      "method": null,
      "native": false
    },
    {
      "column": 22,
      "file": "node:internal/modules/cjs/loader",
      "function": "resolveForCJSWithHooks",
      "line": 1062,
      "method": null,
      "native": false
    },
    {
      "column": 37,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1211,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1487,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 135,
      "method": null,
      "native": false
    },
    {
      "column": 16,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "function": null,
      "line": 4,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    }
  ]
}
2025-06-09 13:24:54 [ERROR]: uncaughtException: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ]
  },
  "exception": true,
  "date": "Mon Jun 09 2025 13:24:54 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 812,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 120737792,
      "heapTotal": 74641408,
      "heapUsed": 44739616,
      "external": 2169237,
      "arrayBuffers": 18639
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1738291.625
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1401,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "defaultResolveImpl",
      "line": 1057,
      "method": null,
      "native": false
    },
    {
      "column": 22,
      "file": "node:internal/modules/cjs/loader",
      "function": "resolveForCJSWithHooks",
      "line": 1062,
      "method": null,
      "native": false
    },
    {
      "column": 37,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1211,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1487,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 135,
      "method": null,
      "native": false
    },
    {
      "column": 16,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "function": null,
      "line": 4,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    }
  ]
}
2025-06-09 13:32:38 [ERROR]: uncaughtException: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ]
  },
  "exception": true,
  "date": "Mon Jun 09 2025 13:32:38 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 31736,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 120918016,
      "heapTotal": 74903552,
      "heapUsed": 44866384,
      "external": 2169237,
      "arrayBuffers": 18639
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1738755.109
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1401,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "defaultResolveImpl",
      "line": 1057,
      "method": null,
      "native": false
    },
    {
      "column": 22,
      "file": "node:internal/modules/cjs/loader",
      "function": "resolveForCJSWithHooks",
      "line": 1062,
      "method": null,
      "native": false
    },
    {
      "column": 37,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1211,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1487,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 135,
      "method": null,
      "native": false
    },
    {
      "column": 16,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "function": null,
      "line": 4,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    }
  ]
}
2025-06-09 13:35:40 [ERROR]: uncaughtException: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ]
  },
  "exception": true,
  "date": "Mon Jun 09 2025 13:35:40 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 34628,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 121319424,
      "heapTotal": 75427840,
      "heapUsed": 44847472,
      "external": 2169237,
      "arrayBuffers": 18639
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1738937.484
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1401,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "defaultResolveImpl",
      "line": 1057,
      "method": null,
      "native": false
    },
    {
      "column": 22,
      "file": "node:internal/modules/cjs/loader",
      "function": "resolveForCJSWithHooks",
      "line": 1062,
      "method": null,
      "native": false
    },
    {
      "column": 37,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1211,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1487,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 135,
      "method": null,
      "native": false
    },
    {
      "column": 16,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "function": null,
      "line": 4,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    }
  ]
}
2025-06-09 13:36:38 [ERROR]: uncaughtException: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
Error: Cannot find module 'bcrypt'
Require stack:
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js
- C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\routes\users.js:4:16)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ]
  },
  "exception": true,
  "date": "Mon Jun 09 2025 13:36:38 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 17312,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 121643008,
      "heapTotal": 75952128,
      "heapUsed": 44594392,
      "external": 2169237,
      "arrayBuffers": 18639
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1738995.187
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1401,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "defaultResolveImpl",
      "line": 1057,
      "method": null,
      "native": false
    },
    {
      "column": 22,
      "file": "node:internal/modules/cjs/loader",
      "function": "resolveForCJSWithHooks",
      "line": 1062,
      "method": null,
      "native": false
    },
    {
      "column": 37,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1211,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1487,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 135,
      "method": null,
      "native": false
    },
    {
      "column": 16,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\routes\\users.js",
      "function": null,
      "line": 4,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    }
  ]
}
2025-06-09 13:39:42 [ERROR]: uncaughtException: app.use() requires a middleware function
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:169:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:169:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
{
  "error": {},
  "exception": true,
  "date": "Mon Jun 09 2025 13:39:42 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 22084,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 121393152,
      "heapTotal": 75952128,
      "heapUsed": 44840536,
      "external": 2169237,
      "arrayBuffers": 18639
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1739179.937
  },
  "trace": [
    {
      "column": 11,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\node_modules\\express\\lib\\application.js",
      "function": "Function.use",
      "line": 217,
      "method": "use",
      "native": false
    },
    {
      "column": 5,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js",
      "function": null,
      "line": 169,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1895,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1465,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1282,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 5,
      "file": "node:internal/modules/run_main",
      "function": "Function.executeUserEntryPoint [as runMain]",
      "line": 170,
      "method": "executeUserEntryPoint [as runMain]",
      "native": false
    },
    {
      "column": 49,
      "file": "node:internal/main/run_main_module",
      "function": null,
      "line": 36,
      "method": null,
      "native": false
    }
  ]
}
2025-06-09 13:39:55 [ERROR]: uncaughtException: app.use() requires a middleware function
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:169:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:169:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
{
  "error": {},
  "exception": true,
  "date": "Mon Jun 09 2025 13:39:55 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 31468,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 121483264,
      "heapTotal": 75952128,
      "heapUsed": 44930312,
      "external": 2169237,
      "arrayBuffers": 18639
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1739192.531
  },
  "trace": [
    {
      "column": 11,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\node_modules\\express\\lib\\application.js",
      "function": "Function.use",
      "line": 217,
      "method": "use",
      "native": false
    },
    {
      "column": 5,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js",
      "function": null,
      "line": 169,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1895,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1465,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1282,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 5,
      "file": "node:internal/modules/run_main",
      "function": "Function.executeUserEntryPoint [as runMain]",
      "line": 170,
      "method": "executeUserEntryPoint [as runMain]",
      "native": false
    },
    {
      "column": 49,
      "file": "node:internal/main/run_main_module",
      "function": null,
      "line": 36,
      "method": null,
      "native": false
    }
  ]
}
2025-06-09 13:41:00 [ERROR]: uncaughtException: app.use() requires a middleware function
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:169:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:169:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
{
  "error": {},
  "exception": true,
  "date": "Mon Jun 09 2025 13:41:00 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 27456,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 115458048,
      "heapTotal": 69636096,
      "heapUsed": 42738280,
      "external": 2169235,
      "arrayBuffers": 18639
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1739257.156
  },
  "trace": [
    {
      "column": 11,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\node_modules\\express\\lib\\application.js",
      "function": "Function.use",
      "line": 217,
      "method": "use",
      "native": false
    },
    {
      "column": 5,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js",
      "function": null,
      "line": 169,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1895,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1465,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1282,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 5,
      "file": "node:internal/modules/run_main",
      "function": "Function.executeUserEntryPoint [as runMain]",
      "line": 170,
      "method": "executeUserEntryPoint [as runMain]",
      "native": false
    },
    {
      "column": 49,
      "file": "node:internal/main/run_main_module",
      "function": null,
      "line": 36,
      "method": null,
      "native": false
    }
  ]
}
2025-06-09 13:42:31 [ERROR]: uncaughtException: app.use() requires a middleware function
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:169:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:169:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
{
  "error": {},
  "exception": true,
  "date": "Mon Jun 09 2025 13:42:31 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 31304,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 115560448,
      "heapTotal": 69373952,
      "heapUsed": 42706640,
      "external": 2169235,
      "arrayBuffers": 18639
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1739348.781
  },
  "trace": [
    {
      "column": 11,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\node_modules\\express\\lib\\application.js",
      "function": "Function.use",
      "line": 217,
      "method": "use",
      "native": false
    },
    {
      "column": 5,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js",
      "function": null,
      "line": 169,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1895,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1465,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1282,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 5,
      "file": "node:internal/modules/run_main",
      "function": "Function.executeUserEntryPoint [as runMain]",
      "line": 170,
      "method": "executeUserEntryPoint [as runMain]",
      "native": false
    },
    {
      "column": 49,
      "file": "node:internal/main/run_main_module",
      "function": null,
      "line": 36,
      "method": null,
      "native": false
    }
  ]
}
2025-06-09 13:44:29 [ERROR]: uncaughtException: app.use() requires a middleware function
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:169:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:169:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
{
  "error": {},
  "exception": true,
  "date": "Mon Jun 09 2025 13:44:29 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 21640,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 115585024,
      "heapTotal": 69636096,
      "heapUsed": 42779368,
      "external": 2169235,
      "arrayBuffers": 18639
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1739466.375
  },
  "trace": [
    {
      "column": 11,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\node_modules\\express\\lib\\application.js",
      "function": "Function.use",
      "line": 217,
      "method": "use",
      "native": false
    },
    {
      "column": 5,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js",
      "function": null,
      "line": 169,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1895,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1465,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1282,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 5,
      "file": "node:internal/modules/run_main",
      "function": "Function.executeUserEntryPoint [as runMain]",
      "line": 170,
      "method": "executeUserEntryPoint [as runMain]",
      "native": false
    },
    {
      "column": 49,
      "file": "node:internal/main/run_main_module",
      "function": null,
      "line": 36,
      "method": null,
      "native": false
    }
  ]
}
2025-06-09 13:46:00 [ERROR]: uncaughtException: app.use() requires a middleware function
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:169:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:169:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
{
  "error": {},
  "exception": true,
  "date": "Mon Jun 09 2025 13:46:00 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 35764,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 120774656,
      "heapTotal": 74641408,
      "heapUsed": 45184664,
      "external": 2169237,
      "arrayBuffers": 18639
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1739557.328
  },
  "trace": [
    {
      "column": 11,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\node_modules\\express\\lib\\application.js",
      "function": "Function.use",
      "line": 217,
      "method": "use",
      "native": false
    },
    {
      "column": 5,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js",
      "function": null,
      "line": 169,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1895,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1465,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1282,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 5,
      "file": "node:internal/modules/run_main",
      "function": "Function.executeUserEntryPoint [as runMain]",
      "line": 170,
      "method": "executeUserEntryPoint [as runMain]",
      "native": false
    },
    {
      "column": 49,
      "file": "node:internal/main/run_main_module",
      "function": null,
      "line": 36,
      "method": null,
      "native": false
    }
  ]
}
2025-06-09 13:46:19 [ERROR]: uncaughtException: app.use() requires a middleware function
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:169:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:169:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
{
  "error": {},
  "exception": true,
  "date": "Mon Jun 09 2025 13:46:18 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 34780,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 120950784,
      "heapTotal": 74903552,
      "heapUsed": 44997368,
      "external": 2169237,
      "arrayBuffers": 18639
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1739576.078
  },
  "trace": [
    {
      "column": 11,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\node_modules\\express\\lib\\application.js",
      "function": "Function.use",
      "line": 217,
      "method": "use",
      "native": false
    },
    {
      "column": 5,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js",
      "function": null,
      "line": 169,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1895,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1465,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1282,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 5,
      "file": "node:internal/modules/run_main",
      "function": "Function.executeUserEntryPoint [as runMain]",
      "line": 170,
      "method": "executeUserEntryPoint [as runMain]",
      "native": false
    },
    {
      "column": 49,
      "file": "node:internal/main/run_main_module",
      "function": null,
      "line": 36,
      "method": null,
      "native": false
    }
  ]
}
2025-06-09 13:47:18 [ERROR]: uncaughtException: app.use() requires a middleware function
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:169:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:169:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
{
  "error": {},
  "exception": true,
  "date": "Mon Jun 09 2025 13:47:18 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 36628,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 121622528,
      "heapTotal": 75689984,
      "heapUsed": 45106976,
      "external": 2169237,
      "arrayBuffers": 18639
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1739635.875
  },
  "trace": [
    {
      "column": 11,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\node_modules\\express\\lib\\application.js",
      "function": "Function.use",
      "line": 217,
      "method": "use",
      "native": false
    },
    {
      "column": 5,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js",
      "function": null,
      "line": 169,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1895,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1465,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1282,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 5,
      "file": "node:internal/modules/run_main",
      "function": "Function.executeUserEntryPoint [as runMain]",
      "line": 170,
      "method": "executeUserEntryPoint [as runMain]",
      "native": false
    },
    {
      "column": 49,
      "file": "node:internal/main/run_main_module",
      "function": null,
      "line": 36,
      "method": null,
      "native": false
    }
  ]
}
2025-06-09 13:47:37 [ERROR]: uncaughtException: app.use() requires a middleware function
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:169:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:169:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
{
  "error": {},
  "exception": true,
  "date": "Mon Jun 09 2025 13:47:37 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 17244,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 122015744,
      "heapTotal": 76214272,
      "heapUsed": 44955296,
      "external": 2169237,
      "arrayBuffers": 18639
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1739654.359
  },
  "trace": [
    {
      "column": 11,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\node_modules\\express\\lib\\application.js",
      "function": "Function.use",
      "line": 217,
      "method": "use",
      "native": false
    },
    {
      "column": 5,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js",
      "function": null,
      "line": 169,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1895,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1465,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1282,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 5,
      "file": "node:internal/modules/run_main",
      "function": "Function.executeUserEntryPoint [as runMain]",
      "line": 170,
      "method": "executeUserEntryPoint [as runMain]",
      "native": false
    },
    {
      "column": 49,
      "file": "node:internal/main/run_main_module",
      "function": null,
      "line": 36,
      "method": null,
      "native": false
    }
  ]
}
