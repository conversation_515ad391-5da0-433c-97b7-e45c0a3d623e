<template>
  <div class="membership-page">
    <div class="membership-header">
      <h1>会员中心</h1>
      <p class="subtitle">享受更多专业服务与优质资源</p>
    </div>
    
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>
    
    <div v-else>
      <!-- 当前会员状态 -->
      <el-card class="status-card" v-if="isAuthenticated">
        <template #header>
          <div class="status-header">
            <span>当前会员状态</span>
            <el-tag :type="userInfo.isMember ? 'success' : 'info'">
              {{ userInfo.isMember ? '已开通' : '未开通' }}
            </el-tag>
          </div>
        </template>
        
        <div v-if="userInfo.isMember" class="member-info">
          <p><strong>会员等级：</strong>{{ userInfo.memberLevel === 1 ? '标准会员' : '高级会员' }}</p>
          <p><strong>到期时间：</strong>{{ userInfo.memberExpireDate || '永久' }}</p>
          <p><strong>自动续费：</strong>{{ userInfo.autoRenew ? '已开启' : '已关闭' }}</p>
          
          <div class="member-actions">
            <el-button type="primary" :disabled="upgradeLoading" @click="handleUpgrade">
              {{ userInfo.memberLevel === 1 ? '升级会员' : '续费会员' }}
            </el-button>
            <el-button :disabled="cancelLoading" @click="handleCancelAutoRenew" v-if="userInfo.autoRenew">
              关闭自动续费
            </el-button>
          </div>
        </div>
        
        <div v-else class="non-member-info">
          <p>您当前不是会员，开通会员可享受更多专业服务</p>
          <div class="member-actions">
            <el-button type="primary" @click="activeTab = 'plans'">立即开通</el-button>
          </div>
        </div>
      </el-card>
      
      <!-- 会员权益与套餐选择 -->
      <el-tabs v-model="activeTab" class="membership-tabs">
        <el-tab-pane label="会员权益" name="benefits">
          <div class="benefits-container">
            <el-row :gutter="20">
              <el-col :span="8" v-for="(benefit, index) in benefits" :key="index">
                <div class="benefit-card">
                  <div class="benefit-icon">{{ benefit.icon }}</div>
                  <h3>{{ benefit.title }}</h3>
                  <p>{{ benefit.description }}</p>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="会员套餐" name="plans">
          <div class="plans-container">
            <el-row :gutter="20">
              <el-col :span="8" v-for="(plan, index) in membershipPlans" :key="plan.id">
                <div class="plan-card" :class="{ 'plan-recommended': plan.recommended }">
                  <div v-if="plan.recommended" class="recommended-badge">推荐</div>
                  <h3>{{ plan.name }}</h3>
                  <div class="plan-price">
                    <span class="currency">¥</span>
                    <span class="amount">{{ plan.price }}</span>
                    <span class="duration">/ {{ plan.duration }}</span>
                  </div>
                  <ul class="plan-features">
                    <li v-for="(feature, idx) in plan.features" :key="idx">
                      <el-icon><Check /></el-icon>
                      <span>{{ feature }}</span>
                    </li>
                  </ul>
                  <el-button 
                    type="primary" 
                    :class="{ 'is-recommended': plan.recommended }"
                    @click="handleSelectPlan(plan)"
                    :disabled="!isAuthenticated"
                  >
                    {{ isAuthenticated ? '立即开通' : '请先登录' }}
                  </el-button>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="常见问题" name="faq">
          <el-collapse>
            <el-collapse-item v-for="(item, index) in faqItems" :key="index" :title="item.question">
              <div v-html="item.answer"></div>
            </el-collapse-item>
          </el-collapse>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <!-- 支付对话框 -->
    <el-dialog v-model="showPaymentDialog" title="确认订单" width="500px">
      <div class="payment-dialog-content">
        <div class="payment-plan-info">
          <h3>{{ selectedPlan.name }}</h3>
          <div class="payment-price">
            <span class="price-label">支付金额：</span>
            <span class="price-value">¥{{ selectedPlan.price }}</span>
          </div>
          <div class="payment-duration">
            有效期：{{ selectedPlan.duration }}
          </div>
        </div>
        
        <el-divider />
        
        <div class="payment-methods">
          <h4>选择支付方式</h4>
          <el-radio-group v-model="paymentMethod">
            <el-radio label="wechat">微信支付</el-radio>
            <el-radio label="alipay">支付宝</el-radio>
          </el-radio-group>
        </div>
        
        <el-checkbox v-model="agreeToTerms" class="terms-checkbox">
          我已阅读并同意<el-link type="primary" @click.stop>《会员服务协议》</el-link>
        </el-checkbox>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showPaymentDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="handlePayment" 
            :disabled="!agreeToTerms || paymentLoading"
            :loading="paymentLoading"
          >
            确认支付
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import { Check } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from '@/api'

const router = useRouter()
const authStore = useAuthStore()
const isAuthenticated = computed(() => authStore.isAuthenticated)
const userInfo = computed(() => authStore.userInfo)

// 页面状态
const loading = ref(true)
const upgradeLoading = ref(false)
const cancelLoading = ref(false)
const paymentLoading = ref(false)
const activeTab = ref('benefits')

// 支付相关
const showPaymentDialog = ref(false)
const selectedPlan = ref({})
const paymentMethod = ref('wechat')
const agreeToTerms = ref(false)

// 会员权益数据
const benefits = [
  {
    icon: '🚀',
    title: '优先回答',
    description: '您的问题将获得优先回答，不必排队等待'
  },
  {
    icon: '📚',
    title: '专属资源',
    description: '获取高质量的教程、案例和模型资源'
  },
  {
    icon: '🔍',
    title: '深度解析',
    description: '问题将获得更加详细的解析和指导'
  },
  {
    icon: '📊',
    title: '高级数据',
    description: '访问高级统计数据和分析工具'
  },
  {
    icon: '🎓',
    title: '专家指导',
    description: '获得专业领域专家的直接指导'
  },
  {
    icon: '💬',
    title: '专属社区',
    description: '加入会员专属讨论组，与同行交流'
  }
]

// 会员套餐数据
const membershipPlans = [
  {
    id: 1,
    name: '月度会员',
    price: 29.9,
    duration: '月',
    recommended: false,
    features: [
      '所有基础问答功能',
      '优先问题解答',
      '专属学习资源',
      '无广告浏览体验'
    ]
  },
  {
    id: 2,
    name: '季度会员',
    price: 79.9,
    duration: '季度',
    recommended: true,
    features: [
      '所有月度会员功能',
      '专家一对一指导 (1次/月)',
      '高级教程和案例',
      '会员专属讨论组'
    ]
  },
  {
    id: 3,
    name: '年度会员',
    price: 299.9,
    duration: '年',
    recommended: false,
    features: [
      '所有季度会员功能',
      '专家一对一指导 (3次/月)',
      'Materials Studio使用培训',
      '优先获取新功能'
    ]
  }
]

// 常见问题
const faqItems = [
  {
    question: '如何开通会员？',
    answer: '在会员套餐页面选择适合您的套餐，点击"立即开通"，按照提示完成支付即可开通会员。'
  },
  {
    question: '会员有哪些权益？',
    answer: '会员可以享受优先回答、专属资源、深度解析、专家指导等多项权益，具体可参考会员权益页面。'
  },
  {
    question: '是否支持自动续费？',
    answer: '是的，会员服务默认为自动续费。在您的会员到期前，系统会自动从您的支付方式中扣除相应费用并续期。如果您不希望自动续费，可以在个人中心中关闭此功能。'
  },
  {
    question: '如何取消会员？',
    answer: '您可以随时在个人中心的会员管理页面中取消会员服务。取消后，您的会员权益将保持到当前订阅周期结束。我们不提供已付费订阅的退款，除非是特殊情况。'
  },
  {
    question: '不同会员等级有什么区别？',
    answer: '不同等级的会员提供不同层次的服务，具体区别可以在会员套餐页面查看。一般而言，更高等级的会员可以享受更多的专家指导和更丰富的学习资源。'
  }
]

// 获取会员信息
const fetchMembershipInfo = async () => {
  try {
    loading.value = true
    
    // 如果已登录，获取用户会员信息
    if (isAuthenticated.value) {
      await authStore.fetchUserInfo()
    }
    
    // 这里可以添加获取会员套餐信息的API调用
    // 现在使用本地数据代替
    
  } catch (err) {
    console.error('获取会员信息失败:', err)
    ElMessage.error('获取会员信息失败，请刷新页面重试')
  } finally {
    loading.value = false
  }
}

// 处理升级会员
const handleUpgrade = () => {
  activeTab.value = 'plans'
}

// 处理取消自动续费
const handleCancelAutoRenew = async () => {
  try {
    const confirmResult = await ElMessageBox.confirm(
      '关闭自动续费后，您的会员服务将在当前订阅周期结束后停止。确定要关闭吗？',
      '确认关闭自动续费',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    if (confirmResult === 'confirm') {
      cancelLoading.value = true
      
      // TODO: 调用取消自动续费API
      // 这里模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 更新本地用户信息
      authStore.updateUserInfo({
        autoRenew: false
      })
      
      ElMessage.success('已关闭自动续费')
    }
  } catch (err) {
    // 用户取消操作或其他错误
    if (err !== 'cancel') {
      console.error('关闭自动续费失败:', err)
      ElMessage.error('关闭自动续费失败，请重试')
    }
  } finally {
    cancelLoading.value = false
  }
}

// 选择会员套餐
const handleSelectPlan = (plan) => {
  selectedPlan.value = plan
  showPaymentDialog.value = true
}

// 处理支付
const handlePayment = async () => {
  if (!agreeToTerms.value) {
    ElMessage.warning('请先同意会员服务协议')
    return
  }
  
  try {
    paymentLoading.value = true
    
    // TODO: 调用支付API
    // 这里模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 支付成功后更新本地用户信息
    authStore.updateUserInfo({
      isMember: true,
      memberLevel: selectedPlan.value.id,
      memberExpireDate: getExpireDate(selectedPlan.value.duration),
      autoRenew: true
    })
    
    // 关闭支付对话框
    showPaymentDialog.value = false
    
    // 显示成功提示
    ElMessage.success('支付成功，会员已开通')
    
    // 重新加载会员信息
    await fetchMembershipInfo()
  } catch (err) {
    console.error('支付失败:', err)
    ElMessage.error('支付失败，请重试')
  } finally {
    paymentLoading.value = false
  }
}

// 计算会员到期时间
const getExpireDate = (duration) => {
  const now = new Date()
  let expireDate = new Date(now)
  
  switch (duration) {
    case '月':
      expireDate.setMonth(now.getMonth() + 1)
      break
    case '季度':
      expireDate.setMonth(now.getMonth() + 3)
      break
    case '年':
      expireDate.setFullYear(now.getFullYear() + 1)
      break
  }
  
  return expireDate.toISOString().split('T')[0]
}

onMounted(() => {
  fetchMembershipInfo()
})
</script>

<style scoped>
.membership-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.membership-header {
  text-align: center;
  margin-bottom: 40px;
}

.membership-header h1 {
  font-size: 36px;
  margin-bottom: 10px;
  color: var(--el-color-primary);
}

.subtitle {
  font-size: 18px;
  color: var(--el-text-color-secondary);
}

.loading-container {
  padding: 40px;
}

/* 会员状态卡片 */
.status-card {
  margin-bottom: 40px;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
}

.member-info, .non-member-info {
  padding: 10px 0;
}

.member-info p, .non-member-info p {
  margin: 10px 0;
  line-height: 1.6;
}

.member-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

/* 会员权益 */
.benefits-container {
  padding: 20px 0;
}

.benefit-card {
  background: white;
  padding: 30px 20px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  height: 100%;
  transition: transform 0.3s;
}

.benefit-card:hover {
  transform: translateY(-5px);
}

.benefit-icon {
  font-size: 40px;
  margin-bottom: 15px;
}

.benefit-card h3 {
  font-size: 18px;
  margin-bottom: 10px;
  color: var(--el-color-primary);
}

.benefit-card p {
  color: var(--el-text-color-regular);
  line-height: 1.6;
}

/* 会员套餐 */
.plans-container {
  padding: 20px 0;
}

.plan-card {
  background: white;
  padding: 30px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s;
}

.plan-card:hover {
  transform: translateY(-5px);
}

.plan-recommended {
  box-shadow: 0 4px 20px rgba(var(--el-color-primary-rgb), 0.3);
  border: 2px solid var(--el-color-primary);
}

.recommended-badge {
  position: absolute;
  top: 0;
  right: 20px;
  background: var(--el-color-primary);
  color: white;
  padding: 2px 10px;
  font-size: 12px;
  border-radius: 0 0 4px 4px;
}

.plan-card h3 {
  font-size: 20px;
  margin-bottom: 15px;
}

.plan-price {
  margin-bottom: 20px;
}

.currency {
  font-size: 20px;
  vertical-align: top;
}

.amount {
  font-size: 36px;
  font-weight: 700;
}

.duration {
  font-size: 16px;
  color: var(--el-text-color-secondary);
}

.plan-features {
  list-style: none;
  padding: 0;
  margin: 0 0 20px;
  text-align: left;
  flex-grow: 1;
}

.plan-features li {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.plan-features .el-icon {
  color: var(--el-color-success);
  margin-right: 8px;
}

.is-recommended {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

/* 支付对话框 */
.payment-dialog-content {
  padding: 20px 0;
}

.payment-plan-info {
  text-align: center;
  margin-bottom: 20px;
}

.payment-plan-info h3 {
  font-size: 20px;
  margin-bottom: 10px;
}

.payment-price {
  margin: 15px 0;
}

.price-label {
  font-size: 16px;
}

.price-value {
  font-size: 24px;
  font-weight: 700;
  color: #f56c6c;
}

.payment-duration {
  color: var(--el-text-color-secondary);
}

.payment-methods {
  margin: 20px 0;
}

.payment-methods h4 {
  margin-bottom: 15px;
}

.terms-checkbox {
  margin-top: 20px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .membership-header h1 {
    font-size: 28px;
  }
  
  .subtitle {
    font-size: 16px;
  }
  
  .benefit-card, .plan-card {
    margin-bottom: 20px;
  }
}
</style>
