// 导入用户数据
import users from './users';

export default [
  {
    id: 1,
    title: 'Materials Studio 2020完全使用指南',
    description: '全面详细的Materials Studio 2020使用教程，包含各模块的基础操作和高级功能。',
    type: 'document',
    format: 'pdf',
    url: 'https://example.com/resources/ms-guide-2020.pdf',
    size: 15.6, // MB
    downloadCount: 324,
    createdAt: '2023-01-15T08:30:00.000Z',
    updatedAt: '2023-01-15T08:30:00.000Z',
    userId: 1,
    user: users.find(u => u.id === 1),
    category: '使用指南',
    tags: ['教程', '入门', '全模块'],
    isFree: false,
    thumbnailUrl: 'https://via.placeholder.com/300x200?text=MS+Guide'
  },
  {
    id: 2,
    title: 'CASTEP模块计算实例',
    description: '包含10个CASTEP计算实例，详细讲解了从模型构建到结果分析的全过程。',
    type: 'document',
    format: 'pdf',
    url: 'https://example.com/resources/castep-examples.pdf',
    size: 8.2, // MB
    downloadCount: 189,
    createdAt: '2023-02-10T14:45:00.000Z',
    updatedAt: '2023-02-10T14:45:00.000Z',
    userId: 1,
    user: users.find(u => u.id === 1),
    category: '实例教程',
    tags: ['CASTEP', 'DFT', '计算实例'],
    isFree: true,
    thumbnailUrl: 'https://via.placeholder.com/300x200?text=CASTEP+Examples'
  },
  {
    id: 3,
    title: '分子动力学模拟视频教程',
    description: '系列视频教程，详细讲解使用Forcite和Discover模块进行分子动力学模拟的方法。',
    type: 'video',
    format: 'mp4',
    url: 'https://example.com/resources/md-tutorial-videos.zip',
    size: 450, // MB
    downloadCount: 156,
    createdAt: '2023-03-05T10:15:00.000Z',
    updatedAt: '2023-03-05T10:15:00.000Z',
    userId: 1,
    user: users.find(u => u.id === 1),
    category: '视频教程',
    tags: ['分子动力学', 'Forcite', 'Discover'],
    isFree: false,
    thumbnailUrl: 'https://via.placeholder.com/300x200?text=MD+Video+Tutorial'
  },
  {
    id: 4,
    title: '常见材料晶体结构库',
    description: '包含100多种常见材料的晶体结构文件，可直接导入Materials Studio使用。',
    type: 'dataset',
    format: 'xsd',
    url: 'https://example.com/resources/crystal-library.zip',
    size: 25.4, // MB
    downloadCount: 412,
    createdAt: '2023-01-20T09:00:00.000Z',
    updatedAt: '2023-04-12T16:30:00.000Z',
    userId: 1,
    user: users.find(u => u.id === 1),
    category: '模型库',
    tags: ['晶体结构', '模型库', 'XSD格式'],
    isFree: true,
    thumbnailUrl: 'https://via.placeholder.com/300x200?text=Crystal+Library'
  },
  {
    id: 5,
    title: 'Materials Studio脚本编程指南',
    description: '详细介绍Materials Studio脚本编程，包含Perl和Python脚本示例，用于自动化计算流程。',
    type: 'document',
    format: 'pdf',
    url: 'https://example.com/resources/ms-scripting-guide.pdf',
    size: 6.8, // MB
    downloadCount: 97,
    createdAt: '2023-05-02T13:20:00.000Z',
    updatedAt: '2023-05-02T13:20:00.000Z',
    userId: 1,
    user: users.find(u => u.id === 1),
    category: '编程指南',
    tags: ['脚本', 'Perl', 'Python', '自动化'],
    isFree: false,
    thumbnailUrl: 'https://via.placeholder.com/300x200?text=Scripting+Guide'
  }
]; 