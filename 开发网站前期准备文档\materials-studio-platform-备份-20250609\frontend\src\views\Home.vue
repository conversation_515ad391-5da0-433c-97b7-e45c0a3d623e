<template>
  <div class="home-page">
    <!-- 顶部导航 -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <div class="logo">
            <h1>🧪 Materials Studio Platform</h1>
          </div>
          <nav class="nav">
            <router-link to="/questions" class="nav-link">问答</router-link>
            <router-link to="/resources" class="nav-link">资源</router-link>
            <router-link to="/membership" class="nav-link">会员</router-link>
          </nav>
          <div class="auth-buttons">
            <template v-if="!isAuthenticated">
              <el-button @click="$router.push('/login')">登录</el-button>
              <el-button type="primary" @click="$router.push('/register')">注册</el-button>
            </template>
            <template v-else>
              <el-dropdown @command="handleUserCommand">
                <span class="user-info">
                  <el-avatar :size="32" :src="userInfo.avatar">
                    {{ userInfo.username?.charAt(0).toUpperCase() }}
                  </el-avatar>
                  <span class="username">{{ userInfo.username }}</span>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                    <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="main">
      <!-- 英雄区域 -->
      <section class="hero">
        <div class="container">
          <div class="hero-content">
            <h2 class="hero-title">Materials Studio 专业问答平台</h2>
            <p class="hero-subtitle">为分子模拟研究人员提供专业的技术支持和学习资源</p>
            <div class="hero-actions">
              <el-button type="primary" size="large" @click="$router.push('/ask')">
                立即提问
              </el-button>
              <el-button size="large" @click="$router.push('/questions')">
                浏览问答
              </el-button>
            </div>
          </div>
        </div>
      </section>

      <!-- 新增数据库功能区块 -->
      <el-row :gutter="20" class="database-section">
        <el-col :span="24">
          <h2 class="section-title">材料数据资源</h2>
          <p class="section-desc">多种专业数据库助力您的研究</p>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="(db, index) in databaseResources" :key="index" class="db-card-wrapper">
          <el-card class="db-card" shadow="hover">
            <div class="db-icon">
              <i :class="db.icon"></i>
            </div>
            <h3>{{ db.name }}</h3>
            <p>{{ db.description }}</p>
            <div class="db-tags">
              <el-tag v-for="(tag, i) in db.tags" :key="i" size="small" :type="tagTypes[i % 5]">{{ tag }}</el-tag>
            </div>
            <el-button type="primary" text>访问数据库</el-button>
          </el-card>
        </el-col>
      </el-row>

      <!-- 特色功能 -->
      <section class="features">
        <div class="container">
          <h3 class="section-title">平台特色</h3>
          <div class="features-grid">
            <div class="feature-card">
              <div class="feature-icon">🎯</div>
              <h4 class="feature-title">专业问答</h4>
              <p class="feature-description">专注Materials Studio软件的专业问答社区</p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">👥</div>
              <h4 class="feature-title">会员制度</h4>
              <p class="feature-description">分级会员制度，享受差异化优质服务</p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">📹</div>
              <h4 class="feature-title">视频教程</h4>
              <p class="feature-description">丰富的视频教程和学习资源</p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">🔒</div>
              <h4 class="feature-title">高质量内容</h4>
              <p class="feature-description">权限控制确保内容质量和专业性</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 最新问题 -->
      <section class="latest-questions">
        <div class="container">
          <div class="section-header">
            <h3 class="section-title">最新问题</h3>
            <el-button text @click="$router.push('/questions')">查看更多</el-button>
          </div>
          <div class="questions-list">
            <div v-if="loading" class="loading-container">
              <el-icon class="is-loading"><Loading /></el-icon>
              <span class="loading-text">加载中...</span>
            </div>
            <div v-else-if="questions.length === 0" class="empty-container">
              <div class="empty-icon">📝</div>
              <div class="empty-text">暂无问题</div>
              <div class="empty-description">成为第一个提问的人吧！</div>
            </div>
            <div v-else class="question-cards">
              <div v-for="question in questions" :key="question.id" class="question-card">
                <h4 class="question-title">
                  <router-link :to="`/questions/${question.id}`">
                    {{ question.title }}
                  </router-link>
                </h4>
                <p class="question-summary">{{ question.summary }}</p>
                <div class="question-meta">
                  <span class="author">{{ question.author }}</span>
                  <span class="time">{{ formatTime(question.createdAt) }}</span>
                  <span class="stats">{{ question.answerCount }} 回答</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h4>Materials Studio Platform</h4>
            <p>专业的分子模拟软件问答平台</p>
          </div>
          <div class="footer-section">
            <h4>快速链接</h4>
            <ul>
              <li><router-link to="/questions">问答</router-link></li>
              <li><router-link to="/resources">资源</router-link></li>
              <li><router-link to="/membership">会员</router-link></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>联系我们</h4>
            <p>邮箱: <EMAIL></p>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 Materials Studio Platform. All rights reserved.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import api from '@/api'

const router = useRouter()
const authStore = useAuthStore()

// 计算属性
const isAuthenticated = computed(() => authStore.isAuthenticated)
const userInfo = computed(() => authStore.userInfo)

// 响应式数据
const loading = ref(false)
const questions = ref([])

// 新增数据库资源数据
const databaseResources = ref([
  {
    name: '晶体结构库',
    description: '包含超过35万种无机晶体材料的结构数据、能带结构及第一性原理计算结果',
    icon: 'el-icon-data-analysis',
    tags: ['无机材料', '晶体结构', '计算数据']
  },
  {
    name: '金属材料库',
    description: '收录全球24万+金属材料数据，包含各国标准、厂商信息及完整性能参数',
    icon: 'el-icon-metal',
    tags: ['金属材料', '标准数据', '性能参数']
  },
  {
    name: '高分子材料库',
    description: '提供聚合物分子结构、加工工艺、物理特性及NMR谱等全面数据',
    icon: 'el-icon-connection',
    tags: ['高分子', '聚合物', '加工数据']
  },
  {
    name: '相图数据库',
    description: '覆盖10000+材料体系的相图数据，包含二元、三元合金相图及相关晶体数据',
    icon: 'el-icon-histogram',
    tags: ['相图', '合金', '热力学数据']
  }
])
const tagTypes = ref(['', 'success', 'warning', 'danger', 'info'])

// 方法
const handleUserCommand = async (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'logout':
      await authStore.logout()
      ElMessage.success('已退出登录')
      break
  }
}

const formatTime = (time) => {
  return new Date(time).toLocaleDateString('zh-CN')
}

const fetchLatestQuestions = async () => {
  try {
    loading.value = true
    const response = await api.questions.list({ limit: 6 })
    if (response.success) {
      questions.value = response.data.data || []
    }
  } catch (error) {
    console.error('获取最新问题失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchLatestQuestions()
})
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
  }

  .logo h1 {
    font-size: 20px;
    font-weight: 600;
    color: var(--el-color-primary);
  }

  .nav {
    display: flex;
    gap: 24px;

    .nav-link {
      color: var(--el-text-color-primary);
      font-weight: 500;
      transition: color 0.3s;

      &:hover, &.router-link-active {
        color: var(--el-color-primary);
      }
    }
  }

  .auth-buttons {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;

    .username {
      font-weight: 500;
    }
  }
}

.main {
  flex: 1;
}

.hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .hero-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
  }

  .hero-title {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 16px;
  }

  .hero-subtitle {
    font-size: 20px;
    margin-bottom: 32px;
    opacity: 0.9;
  }

  .hero-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
  }
}

.features {
  padding: 80px 0;
  background: #f8f9fa;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .section-title {
    text-align: center;
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 48px;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 32px;
  }

  .feature-card {
    background: white;
    padding: 32px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;

    &:hover {
      transform: translateY(-4px);
    }
  }

  .feature-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .feature-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 12px;
  }

  .feature-description {
    color: var(--el-text-color-regular);
    line-height: 1.6;
  }
}

.latest-questions {
  padding: 80px 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
  }

  .section-title {
    font-size: 32px;
    font-weight: 600;
  }

  .question-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 24px;
  }

  .question-card {
    background: white;
    padding: 24px;
    border-radius: 8px;
    border: 1px solid var(--el-border-color-light);
    transition: box-shadow 0.3s;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .question-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;

    a {
      color: var(--el-text-color-primary);
      
      &:hover {
        color: var(--el-color-primary);
      }
    }
  }

  .question-summary {
    color: var(--el-text-color-regular);
    margin-bottom: 12px;
    line-height: 1.5;
  }

  .question-meta {
    display: flex;
    gap: 16px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
}

.footer {
  background: #2c3e50;
  color: white;
  padding: 40px 0 20px;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 32px;
    margin-bottom: 32px;
  }

  .footer-section h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
  }

  .footer-section ul {
    list-style: none;
  }

  .footer-section li {
    margin-bottom: 8px;
  }

  .footer-section a {
    color: rgba(255, 255, 255, 0.8);
    
    &:hover {
      color: white;
    }
  }

  .footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 32px;
  }

  .hero-subtitle {
    font-size: 16px;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .question-cards {
    grid-template-columns: 1fr;
  }
}

.database-section {
  margin: 40px 0;
  
  .section-title {
    text-align: center;
    margin-bottom: 10px;
    font-size: 28px;
    font-weight: 600;
  }
  
  .section-desc {
    text-align: center;
    color: #666;
    margin-bottom: 30px;
  }
  
  .db-card-wrapper {
    margin-bottom: 20px;
  }
  
  .db-card {
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s;
    
    &:hover {
      transform: translateY(-5px);
    }
    
    .db-icon {
      font-size: 40px;
      margin-bottom: 15px;
      color: #409EFF;
    }
    
    h3 {
      margin: 10px 0;
      font-size: 18px;
    }
    
    p {
      flex-grow: 1;
      color: #666;
      font-size: 14px;
      line-height: 1.5;
      margin-bottom: 15px;
    }
    
    .db-tags {
      margin-bottom: 15px;
      
      .el-tag {
        margin-right: 5px;
        margin-bottom: 5px;
      }
    }
  }
}
</style>
